@echo off
echo ========================================
echo   Authentification AquaTrack Simple
echo ========================================
echo.

echo 1. Démarrage du serveur Node.js...
echo.
echo Serveur d'authentification:
echo - Port: 4000
echo - Base de données: Facturation
echo - Table: utilisateur
echo.
echo Redirections automatiques:
echo - Role Tech → TechnicianDashboard
echo - Role Admin → Dashboard
echo.
echo Comptes de test:
echo - Admin: <EMAIL> / Admin123
echo - Tech: <EMAIL> / Tech123
echo.

start "Serveur Auth" cmd /k "node AuthenticationServer.js"

echo 2. Attente du démarrage du serveur...
timeout /t 3 /nobreak >nul

echo 3. Création des utilisateurs de test...
curl -X POST http://localhost:4000/api/create-test-users

echo.
echo ========================================
echo   Serveur démarré avec succès !
echo ========================================
echo.
echo Pour utiliser l'authentification:
echo 1. Intégrez AuthenticationMobile.js dans votre app React Native
echo 2. Configurez la navigation vers TechnicianDashboard et Dashboard
echo 3. Testez avec les comptes fournis
echo.

pause
