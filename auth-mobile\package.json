{"name": "aquatrack-auth-mobile", "version": "1.0.0", "main": "App.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~50.0.0", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.6", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "react-native-screens": "~3.29.0", "react-native-safe-area-context": "4.8.2", "react-native-gesture-handler": "~2.14.0", "@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-picker/picker": "2.6.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true, "description": "Application mobile d'authentification AquaTrack avec React Native", "keywords": ["react-native", "expo", "authentication", "mobile", "aquatrack"], "author": "AquaTrack Team", "license": "MIT"}