# 🔐 Système d'Authentification AquaTrack Complet

## 📋 Vue d'Ensemble

Système d'authentification moderne avec **React Native** (frontend mobile) et **Node.js/Express** (backend) utilisant votre table `utilisateur` de la base de données "Facturation".

## 🏗️ Architecture

```
📁 Projet d'Authentification AquaTrack
├── 📁 auth-backend/          # Backend Node.js/Express
│   ├── server.js             # Serveur principal
│   ├── package.json          # Dépendances backend
│   ├── create-test-users.js  # Script de création d'utilisateurs
│   └── 📁 database/
│       ├── init-auth.sql     # Initialisation de la table
│       └── test-users.sql    # Données de test
├── 📁 auth-mobile/           # Frontend React Native
│   ├── App.js                # Application principale
│   ├── package.json          # Dépendances mobile
│   └── 📁 screens/
│       ├── LoginScreen.js    # Écran de connexion
│       ├── RegisterScreen.js # Écran d'inscription
│       └── DashboardScreen.js# Tableau de bord
└── 📁 Scripts de démarrage
    ├── start-auth-backend.bat # Démarrer le backend
    ├── start-auth-mobile.bat  # Démarrer l'app mobile
    └── test-auth-system.js    # Tests automatisés
```

## 🚀 Démarrage Rapide

### 1. **Démarrer le Backend**
```bash
# Double-cliquez sur:
start-auth-backend.bat

# Ou manuellement:
cd auth-backend
npm install
node create-test-users.js
npm start
```

### 2. **Démarrer l'Application Mobile**
```bash
# Double-cliquez sur:
start-auth-mobile.bat

# Ou manuellement:
cd auth-mobile
npm install
npm start
```

### 3. **Tester le Système**
```bash
node test-auth-system.js
```

## 🔑 Comptes de Test

| Rôle | Email | Mot de passe | Description |
|------|-------|--------------|-------------|
| **Admin** | `<EMAIL>` | `Admin123` | Administrateur principal |
| **Tech** | `<EMAIL>` | `Tech123` | Technicien principal |
| **Tech** | `<EMAIL>` | `Ahmed123` | Technicien Ahmed |
| **Admin** | `<EMAIL>` | `Fatima123` | Admin Fatima |
| **Tech** | `<EMAIL>` | `Mohamed123` | Technicien Mohamed |

## 🛠️ Fonctionnalités

### 🔐 **Authentification**
- ✅ Connexion avec email/mot de passe
- ✅ Inscription de nouveaux utilisateurs
- ✅ Hachage sécurisé des mots de passe (bcrypt)
- ✅ Tokens JWT pour la session
- ✅ Déconnexion sécurisée

### 👤 **Gestion des Utilisateurs**
- ✅ Profils utilisateur complets
- ✅ Rôles : Admin / Technicien
- ✅ Informations personnelles
- ✅ Protection des comptes système

### 📱 **Interface Mobile**
- ✅ Design moderne et responsive
- ✅ Navigation fluide
- ✅ Gestion d'état avec AsyncStorage
- ✅ Validation des formulaires
- ✅ Messages d'erreur informatifs

## 🔧 API Endpoints

### **Authentification**
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin123"
}
```

### **Inscription**
```http
POST /api/auth/register
Content-Type: application/json

{
  "nom": "Dupont",
  "prenom": "Jean",
  "email": "<EMAIL>",
  "password": "MotDePasse123",
  "adresse": "123 Rue Example",
  "tel": "+216 12 345 678",
  "role": "Tech"
}
```

### **Profil Utilisateur**
```http
GET /api/auth/profile
Authorization: Bearer <token>
```

### **Liste des Utilisateurs** (Admin seulement)
```http
GET /api/auth/users
Authorization: Bearer <token>
```

## 🗄️ Base de Données

### **Table utilisateur**
```sql
CREATE TABLE utilisateur (
    idtech SERIAL PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    adresse VARCHAR(255),
    tel VARCHAR(20),
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL,
    role VARCHAR(10) DEFAULT 'Tech' CHECK (role IN ('Admin', 'Tech')),
    is_protected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔒 Sécurité

- **Mots de passe hachés** avec bcrypt (10 rounds)
- **Tokens JWT** avec expiration (24h)
- **Validation des entrées** côté client et serveur
- **Protection CORS** configurée
- **Comptes protégés** contre la suppression

## 📱 Utilisation Mobile

1. **Installer Expo Go** sur votre téléphone
2. **Démarrer l'application** avec `start-auth-mobile.bat`
3. **Scanner le QR code** avec Expo Go
4. **Se connecter** avec un compte de test
5. **Explorer** les fonctionnalités

## 🧪 Tests

Le script `test-auth-system.js` teste automatiquement :
- ✅ Connexion au serveur
- ✅ Authentification des utilisateurs
- ✅ Inscription de nouveaux comptes
- ✅ Récupération des profils
- ✅ Gestion des permissions (Admin/Tech)

## 🔧 Configuration

### **Backend (auth-backend/server.js)**
```javascript
// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});
```

### **Mobile (auth-mobile/screens/LoginScreen.js)**
```javascript
// Configuration de l'API
const API_BASE_URL = 'http://localhost:4000';
```

## 🎯 Prochaines Étapes

1. **Tester** le système complet
2. **Personnaliser** l'interface selon vos besoins
3. **Ajouter** des fonctionnalités spécifiques
4. **Intégrer** avec vos autres modules AquaTrack
5. **Déployer** en production

---

**✅ Système d'authentification complet prêt à l'emploi !**

Votre application mobile React Native avec backend Node.js est maintenant fonctionnelle avec votre base de données "Facturation".
