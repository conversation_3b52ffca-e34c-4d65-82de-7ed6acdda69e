{"name": "aquatrack-auth-backend", "version": "1.0.0", "description": "Backend d'authentification pour AquaTrack avec Node.js et PostgreSQL", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "postgresql", "authentication", "jwt", "bcrypt", "aquatrack"], "author": "AquaTrack Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}