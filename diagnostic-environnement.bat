@echo off
echo ========================================
echo   Diagnostic de l'Environnement
echo ========================================
echo.

echo 1. Vérification de Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERREUR: Node.js n'est pas installé
) else (
    echo ✓ Node.js installé
)
echo.

echo 2. Vérification de npm...
npm --version
if %errorlevel% neq 0 (
    echo ERREUR: npm n'est pas installé
) else (
    echo ✓ npm installé
)
echo.

echo 3. Vérification d'Expo CLI...
npx expo --version
if %errorlevel% neq 0 (
    echo ERREUR: Expo CLI n'est pas accessible
) else (
    echo ✓ Expo CLI accessible
)
echo.

echo 4. Vérification d'Android Studio (optionnel)...
where adb >nul 2>nul
if %errorlevel% neq 0 (
    echo ⚠ Android Studio/ADB non configuré (optionnel pour Expo Go)
) else (
    echo ✓ Android Studio/ADB configuré
    adb devices
)
echo.

echo 5. Vérification du projet React Native...
cd react-native
if exist package.json (
    echo ✓ Projet React Native trouvé
    echo Dépendances principales:
    npm list --depth=0 expo react react-native
) else (
    echo ERREUR: Fichier package.json non trouvé
)
echo.

echo 6. Vérification des dépendances...
if exist node_modules (
    echo ✓ Dépendances installées
) else (
    echo ⚠ Dépendances non installées - Exécutez 'npm install'
)
echo.

echo ========================================
echo   Résumé des Solutions Disponibles
echo ========================================
echo.
echo 1. EXPO GO (Recommandé):
echo    - Installez Expo Go sur votre téléphone
echo    - Lancez: start-expo-mobile.bat
echo    - Scannez le QR code
echo.
echo 2. NAVIGATEUR WEB:
echo    - Lancez: start-expo-mobile.bat
echo    - Appuyez sur 'w' dans le terminal
echo.
echo 3. ÉMULATEUR ANDROID (Avancé):
echo    - Installez Android Studio
echo    - Configurez un AVD
echo    - Lancez: start-android-emulator.bat
echo.

pause
