{"version": 3, "names": ["checkValidArgs", "keyValuePairs", "callback", "Array", "isArray", "length", "Error", "checkValidInput", "input", "key", "value", "console", "warn", "convertError", "error", "out", "message", "convertErrors", "errs", "errors", "ensureArray", "map", "e"], "sources": ["helpers.ts"], "sourcesContent": ["import type { ErrorLike } from \"./types\";\n\nexport function checkValidArgs(keyValuePairs: unknown[], callback: unknown) {\n  if (\n    !Array.isArray(keyValuePairs) ||\n    keyValuePairs.length === 0 ||\n    !Array.isArray(keyValuePairs[0])\n  ) {\n    throw new Error(\n      \"[AsyncStorage] Expected array of key-value pairs as first argument to multiSet\"\n    );\n  }\n\n  if (callback && typeof callback !== \"function\") {\n    if (Array.isArray(callback)) {\n      throw new Error(\n        \"[AsyncStorage] Expected function as second argument to multiSet. Did you forget to wrap key-value pairs in an array for the first argument?\"\n      );\n    }\n\n    throw new Error(\n      \"[AsyncStorage] Expected function as second argument to multiSet\"\n    );\n  }\n}\n\nexport function checkValidInput(...input: unknown[]) {\n  const [key, value] = input;\n\n  if (typeof key !== \"string\") {\n    // eslint-disable-next-line no-console\n    console.warn(\n      `[AsyncStorage] Using ${typeof key} type for key is not supported. This can lead to unexpected behavior/errors. Use string instead.\\nKey passed: ${key}\\n`\n    );\n  }\n\n  if (input.length > 1 && typeof value !== \"string\") {\n    if (value == null) {\n      throw new Error(\n        `[AsyncStorage] Passing null/undefined as value is not supported. If you want to remove value, Use .removeItem method instead.\\nPassed value: ${value}\\nPassed key: ${key}\\n`\n      );\n    } else {\n      // eslint-disable-next-line no-console\n      console.warn(\n        `[AsyncStorage] The value for key \"${key}\" is not a string. This can lead to unexpected behavior/errors. Consider stringifying it.\\nPassed value: ${value}\\nPassed key: ${key}\\n`\n      );\n    }\n  }\n}\n\nexport function convertError(error?: ErrorLike): Error | null {\n  if (!error) {\n    return null;\n  }\n\n  const out = new Error(error.message) as Error & ErrorLike;\n  out[\"key\"] = error.key;\n  return out;\n}\n\nexport function convertErrors(\n  errs?: ErrorLike[]\n): ReadonlyArray<Error | null> | null {\n  const errors = ensureArray(errs);\n  return errors ? errors.map((e) => convertError(e)) : null;\n}\n\nfunction ensureArray(e?: ErrorLike | ErrorLike[]): ErrorLike[] | null {\n  if (Array.isArray(e)) {\n    return e.length === 0 ? null : e;\n  } else if (e) {\n    return [e];\n  } else {\n    return null;\n  }\n}\n"], "mappings": ";;;;;;;;;AAEO,SAASA,cAAcA,CAACC,aAAwB,EAAEC,QAAiB,EAAE;EAC1E,IACE,CAACC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,IAC7BA,aAAa,CAACI,MAAM,KAAK,CAAC,IAC1B,CAACF,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,CAAC,CAAC,CAAC,EAChC;IACA,MAAM,IAAIK,KAAK,CACb,gFACF,CAAC;EACH;EAEA,IAAIJ,QAAQ,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IAC9C,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;MAC3B,MAAM,IAAII,KAAK,CACb,6IACF,CAAC;IACH;IAEA,MAAM,IAAIA,KAAK,CACb,iEACF,CAAC;EACH;AACF;AAEO,SAASC,eAAeA,CAAC,GAAGC,KAAgB,EAAE;EACnD,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAGF,KAAK;EAE1B,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;IAC3B;IACAE,OAAO,CAACC,IAAI,CACT,wBAAuB,OAAOH,GAAI,iHAAgHA,GAAI,IACzJ,CAAC;EACH;EAEA,IAAID,KAAK,CAACH,MAAM,GAAG,CAAC,IAAI,OAAOK,KAAK,KAAK,QAAQ,EAAE;IACjD,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,MAAM,IAAIJ,KAAK,CACZ,gJAA+II,KAAM,iBAAgBD,GAAI,IAC5K,CAAC;IACH,CAAC,MAAM;MACL;MACAE,OAAO,CAACC,IAAI,CACT,qCAAoCH,GAAI,4GAA2GC,KAAM,iBAAgBD,GAAI,IAChL,CAAC;IACH;EACF;AACF;AAEO,SAASI,YAAYA,CAACC,KAAiB,EAAgB;EAC5D,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAEA,MAAMC,GAAG,GAAG,IAAIT,KAAK,CAACQ,KAAK,CAACE,OAAO,CAAsB;EACzDD,GAAG,CAAC,KAAK,CAAC,GAAGD,KAAK,CAACL,GAAG;EACtB,OAAOM,GAAG;AACZ;AAEO,SAASE,aAAaA,CAC3BC,IAAkB,EACkB;EACpC,MAAMC,MAAM,GAAGC,WAAW,CAACF,IAAI,CAAC;EAChC,OAAOC,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAEC,CAAC,IAAKT,YAAY,CAACS,CAAC,CAAC,CAAC,GAAG,IAAI;AAC3D;AAEA,SAASF,WAAWA,CAACE,CAA2B,EAAsB;EACpE,IAAInB,KAAK,CAACC,OAAO,CAACkB,CAAC,CAAC,EAAE;IACpB,OAAOA,CAAC,CAACjB,MAAM,KAAK,CAAC,GAAG,IAAI,GAAGiB,CAAC;EAClC,CAAC,MAAM,IAAIA,CAAC,EAAE;IACZ,OAAO,CAACA,CAAC,CAAC;EACZ,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF"}