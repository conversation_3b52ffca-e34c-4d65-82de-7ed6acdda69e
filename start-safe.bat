@echo off
echo ========================================
echo   Démarrage Sécurisé React Native
echo ========================================
echo.

cd react-native

echo 1. Vérification de l'environnement...
if not exist node_modules (
    echo Installation des dépendances...
    call npm install
)

echo 2. Nettoyage préventif...
if exist .expo rmdir /s /q .expo
if exist node_modules\.cache rmdir /s /q node_modules\.cache

echo 3. Démarrage avec configuration sécurisée...
call npx expo start --tunnel --reset-cache --clear

echo.
echo ========================================
echo   Application démarrée
echo ========================================
echo.
echo IMPORTANT:
echo - Utilisez le QR code qui apparaît maintenant
echo - Assurez-vous que votre téléphone et PC sont sur le même réseau
echo - Si erreur persiste, redémarrez votre téléphone
echo.

pause
