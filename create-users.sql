-- Script pour créer des utilisateurs de test dans la table utilisateur
-- Base de données: Facturation

-- Supprimer les utilisateurs de test existants (optionnel)
DELETE FROM utilisateur WHERE email IN (
    '<EMAIL>',
    '<EMAIL>'
);

-- Insérer les utilisateurs de test avec mots de passe en clair
-- (Le serveur supporte les mots de passe en clair et hachés)

-- 1. Administrateur
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) VALUES
(
    'Admin',
    'Système',
    '123 Avenue Principale, Tunis',
    '+216 71 123 456',
    '<EMAIL>',
    'Admin123',
    'Admin',
    true
);

-- 2. Technicien
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) VALUES
(
    'Technicien',
    'Test',
    '456 Rue Secondaire, Sfax',
    '+216 74 987 654',
    '<EMAIL>',
    'Tech123',
    'Tech',
    true
);

-- Vérification des utilisateurs créés
SELECT 
    idtech,
    nom,
    prenom,
    email,
    role,
    is_protected
FROM utilisateur 
WHERE email IN ('<EMAIL>', '<EMAIL>')
ORDER BY role DESC, nom;

-- Affichage du message de confirmation
SELECT 'Utilisateurs de test créés avec succès!' as message;
