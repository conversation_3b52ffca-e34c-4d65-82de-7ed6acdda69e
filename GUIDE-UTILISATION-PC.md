# 🖥️ Guide - Utilisation sur PC

## ✅ Votre Projet Mobile sur PC !

Votre application React Native fonctionne maintenant parfaitement sur PC dans le navigateur web.

## 🚀 Comment Démarrer sur PC

### **Option 1 : Script Automatique (Recommandé)**
```bash
# Double-cliquez sur :
start-pc-web.bat
```

### **Option 2 : D<PERSON><PERSON><PERSON> Manuel**
```bash
# Terminal 1 - Serveur d'authentification
node AuthenticationServer.js

# Terminal 2 - Application React Native Web
cd react-native
npx expo start --web --port 8082
```

### **Option 3 : Commandes Séparées**
```bash
# 1. Démarrer le serveur d'auth
node AuthenticationServer.js

# 2. Dans un autre terminal, démarrer l'app
cd react-native
npx expo start --web --port 8082

# 3. Ouvrir le navigateur sur :
http://localhost:8082
```

## 🌐 URLs Importantes

| Service | URL | Description |
|---------|-----|-------------|
| **Application** | http://localhost:8082 | Interface d'authentification |
| **Serveur Auth** | http://localhost:4000 | API d'authentification |
| **Test Serveur** | http://localhost:4000 | Vérification du serveur |

## 🔑 Comptes de Test

| Email | Mot de passe | Rôle | Redirection |
|-------|--------------|------|-------------|
| `<EMAIL>` | `Admin123` | Admin | → Dashboard |
| `<EMAIL>` | `Tech123` | Tech | → TechnicianDashboard |

## 📱 Interface sur PC

### **Fonctionnalités Disponibles :**
- ✅ **Formulaire d'authentification** complet
- ✅ **Boutons de test rapide** (Admin/Tech)
- ✅ **Interface responsive** adaptée au navigateur
- ✅ **Connexion à votre base** "Facturation"
- ✅ **Redirection automatique** selon le rôle
- ✅ **Messages d'erreur** informatifs

### **Comment Utiliser :**
1. **Ouvrez** http://localhost:8082 dans votre navigateur
2. **Cliquez** sur "Admin" ou "Tech" pour remplir automatiquement
3. **Cliquez** sur "Se connecter"
4. **Vérifiez** la redirection selon le rôle

## 🗄️ Configuration Base de Données

### **Créer les Utilisateurs de Test :**
```sql
-- Dans votre base "Facturation"
INSERT INTO utilisateur (nom, prenom, email, password, role, is_protected) VALUES
('Admin', 'Système', '<EMAIL>', 'Admin123', 'Admin', true),
('Technicien', 'Test', '<EMAIL>', 'Tech123', 'Tech', true);
```

### **Vérifier les Utilisateurs :**
```sql
SELECT idtech, nom, prenom, email, role 
FROM utilisateur 
WHERE email IN ('<EMAIL>', '<EMAIL>');
```

## 🔧 Dépannage

### **Si l'application ne s'affiche pas :**
1. **Vérifiez** que le port 8082 est libre
2. **Redémarrez** avec : `npx expo start --web --port 8082 --clear`
3. **Ouvrez manuellement** : http://localhost:8082

### **Si la connexion échoue :**
1. **Vérifiez** que le serveur d'auth fonctionne : http://localhost:4000
2. **Vérifiez** que les utilisateurs existent dans la base
3. **Consultez** les logs du serveur dans le terminal

### **Si la base de données ne répond pas :**
1. **Vérifiez** que PostgreSQL fonctionne
2. **Vérifiez** que la base "Facturation" existe
3. **Testez** la connexion avec pgAdmin ou un autre client

## 🎯 Avantages de la Version PC

### **Développement Plus Facile :**
- ✅ **Pas besoin** d'Expo Go ou de téléphone
- ✅ **Outils de développement** du navigateur (F12)
- ✅ **Rechargement automatique** lors des modifications
- ✅ **Console JavaScript** pour le debug

### **Interface Optimisée :**
- ✅ **Responsive design** qui s'adapte à l'écran
- ✅ **Interactions souris** et clavier
- ✅ **Copier-coller** facile pour les tests
- ✅ **Zoom** et redimensionnement

### **Tests Plus Rapides :**
- ✅ **Pas de scan QR** nécessaire
- ✅ **Rechargement instantané**
- ✅ **Accès direct** aux outils de debug
- ✅ **Tests automatisés** possibles

## 📂 Structure du Projet

```
samle-react-app/
├── AuthenticationServer.js     # Serveur Node.js
├── start-pc-web.bat           # Script de démarrage PC
├── react-native/
│   ├── AuthApp.js             # Application d'authentification
│   ├── package.json           # Configuration React Native
│   └── screens/               # Écrans de l'application
└── GUIDE-UTILISATION-PC.md    # Ce guide
```

## 🔄 Workflow de Développement

### **1. Développement :**
```bash
# Démarrer en mode développement
start-pc-web.bat
# Ou manuellement :
cd react-native && npx expo start --web --port 8082
```

### **2. Tests :**
- **Testez** l'authentification avec les comptes fournis
- **Vérifiez** les redirections selon les rôles
- **Consultez** la console pour les erreurs

### **3. Modifications :**
- **Éditez** les fichiers dans `react-native/`
- **Sauvegardez** - le rechargement est automatique
- **Testez** immédiatement dans le navigateur

## 🎨 Personnalisation

### **Modifier l'Interface :**
- **Éditez** `react-native/AuthApp.js`
- **Changez** les couleurs, textes, layout
- **Ajoutez** de nouveaux champs ou fonctionnalités

### **Ajouter des Utilisateurs :**
```sql
INSERT INTO utilisateur (nom, prenom, email, password, role, is_protected)
VALUES ('Nouveau', 'Utilisateur', '<EMAIL>', 'motdepasse', 'Tech', false);
```

### **Modifier les Redirections :**
- **Éditez** `AuthenticationServer.js`
- **Changez** la logique de redirection selon vos besoins

---

**✅ Votre application mobile fonctionne maintenant parfaitement sur PC !**

Utilisez http://localhost:8082 pour accéder à votre interface d'authentification dans le navigateur.
