@echo off
echo ========================================
echo   Démarrage du Projet AquaTrack avec Authentification
echo ========================================
echo.

echo 1. Démarrage du serveur d'authentification...
start "Serveur Auth" cmd /k "node AuthenticationServer.js"

echo 2. Attente du démarrage du serveur...
timeout /t 5 /nobreak >nul

echo 3. Démarrage de l'application React Native...
cd react-native
start "App Mobile" cmd /k "npx expo start --web --port 8082"

echo.
echo ========================================
echo   Projet démarré avec succès !
echo ========================================
echo.
echo Serveur d'authentification: http://localhost:4000
echo Application mobile: http://localhost:8082
echo.
echo Comptes de test:
echo - Admin: <EMAIL> / Admin123 → Dashboard
echo - Tech: <EMAIL> / Tech123 → TechnicianDashboard
echo.
echo Instructions:
echo 1. Ouvrez http://localhost:8082 dans votre navigateur
echo 2. Connectez-vous avec un compte de test
echo 3. Vous serez redirigé selon votre rôle
echo.

pause
