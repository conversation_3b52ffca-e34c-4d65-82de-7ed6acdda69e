/**
 * Copyright © 2022 650 Industries.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export declare function fetchThenEvalAsync(url: string, { scriptType, nonce, crossOrigin, }?: {
    scriptType?: string;
    nonce?: string;
    crossOrigin?: string;
}): Promise<void>;
//# sourceMappingURL=fetchThenEval.web.d.ts.map