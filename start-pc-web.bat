@echo off
echo ========================================
echo   Démarrage AquaTrack sur PC (Web)
echo ========================================
echo.

echo 1. Démarrage du serveur d'authentification...
start "Serveur Auth" cmd /k "node AuthenticationServer.js"

echo 2. Attente du démarrage du serveur...
timeout /t 3 /nobreak >nul

echo 3. Démarrage de l'application React Native en mode Web...
cd react-native
start "AquaTrack Web" cmd /k "npx expo start --web --port 8082"

echo.
echo ========================================
echo   Application démarrée sur PC !
echo ========================================
echo.
echo 🌐 URLs:
echo   • Application: http://localhost:8082
echo   • Serveur Auth: http://localhost:4000
echo.
echo 🔑 Comptes de test:
echo   • Admin: <EMAIL> / Admin123
echo   • Tech: <EMAIL> / Tech123
echo.
echo 📝 Instructions:
echo   1. L'application s'ouvrira automatiquement dans votre navigateur
echo   2. Si elle ne s'ouvre pas, allez sur: http://localhost:8082
echo   3. Utilisez les boutons Admin/Tech pour remplir rapidement
echo   4. Testez l'authentification
echo.

pause
