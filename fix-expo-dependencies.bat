@echo off
echo ========================================
echo   Correction des Dépendances Expo
echo ========================================
echo.

cd react-native

echo 1. Mise à jour des dépendances Expo...
npx expo install --check

echo.
echo 2. Installation des versions compatibles...
npx expo install expo@53.0.19 expo-status-bar@~2.2.3 react@19.0.0 react-native@0.79.5

echo.
echo 3. Mise à jour des packages React Native...
npx expo install react-native-screens@~4.11.1 react-native-safe-area-context@5.4.0

echo.
echo 4. Mise à jour des packages de navigation...
npx expo install react-native-gesture-handler@~2.24.0 react-native-reanimated@~3.17.4

echo.
echo 5. Mise à jour des autres packages...
npx expo install @react-native-picker/picker@2.11.1 expo-location@~18.1.6 react-native-maps@1.20.1

echo.
echo 6. Mise à jour des packages de fichiers...
npx expo install expo-file-system@~18.1.11 expo-sharing@~13.1.5

echo.
echo 7. Nettoyage du cache...
npx expo start --clear

echo.
echo ========================================
echo   Correction terminée !
echo ========================================
echo.
echo Vous pouvez maintenant lancer votre application avec:
echo   start-expo-mobile.bat
echo.

pause
