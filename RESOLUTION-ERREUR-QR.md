# 🔧 Résolution Erreur QR Code Expo

## ❌ Problème Identifié
L'erreur "Something went wrong" lors du scan du QR code Expo est courante et peut être résolue de plusieurs façons.

## ✅ Solutions Disponibles

### **Solution 1 : Mode Tunnel (Recommandé)**

#### **Étapes :**
1. **Démarrez en mode tunnel :**
   ```bash
   # Double-cliquez sur :
   start-expo-tunnel.bat
   ```

2. **Ou manuellement :**
   ```bash
   cd react-native
   npx expo start --tunnel
   ```

3. **Scannez le nouveau QR code** qui apparaît
4. **Si erreur persiste**, utilisez le lien direct affiché

#### **Avantages :**
- ✅ Fonctionne même avec des réseaux complexes
- ✅ Pas besoin de configuration IP
- ✅ Compatible avec tous les téléphones

---

### **Solution 2 : Version Web Mobile**

#### **Étapes :**
1. **Trouvez l'IP de votre PC :**
   ```bash
   # Dans le terminal Windows :
   ipconfig
   # Notez l'adresse IPv4 (ex: *************)
   ```

2. **Démarrez la version web :**
   ```bash
   # Double-cliquez sur :
   start-web-mobile.bat
   ```

3. **Sur votre téléphone :**
   - Ouvrez le navigateur
   - Allez sur : `http://[IP_DE_VOTRE_PC]:8082`
   - Exemple : `http://*************:8082`

#### **Avantages :**
- ✅ Pas besoin d'Expo Go
- ✅ Fonctionne dans le navigateur mobile
- ✅ Plus stable

---

### **Solution 3 : Version Mobile Simplifiée**

#### **Étapes :**
1. **Modifiez package.json :**
   ```json
   // Dans react-native/package.json
   "main": "AuthAppSimple.js"
   ```

2. **Modifiez l'IP dans AuthAppSimple.js :**
   ```javascript
   // Ligne 13 dans AuthAppSimple.js
   const API_BASE_URL = 'http://[VOTRE_IP]:4000';
   // Remplacez [VOTRE_IP] par l'IP de votre PC
   ```

3. **Démarrez l'application :**
   ```bash
   cd react-native
   npx expo start --tunnel
   ```

#### **Avantages :**
- ✅ Version optimisée pour mobile
- ✅ Interface simplifiée
- ✅ Moins de dépendances

---

## 🔧 Étapes de Dépannage

### **Étape 1 : Vérifications de Base**
```bash
# 1. Vérifiez que votre téléphone et PC sont sur le même WiFi
# 2. Vérifiez qu'Expo Go est installé sur votre téléphone
# 3. Vérifiez que le serveur d'auth fonctionne
node AuthenticationServer.js
```

### **Étape 2 : Trouvez Votre IP**
```bash
# Windows
ipconfig

# Cherchez "Carte réseau sans fil Wi-Fi"
# Notez l'adresse IPv4 (ex: *************)
```

### **Étape 3 : Testez la Connexion**
```bash
# Sur votre téléphone, testez dans le navigateur :
http://[VOTRE_IP]:8082
http://[VOTRE_IP]:4000

# Si ça fonctionne, le problème vient d'Expo Go
```

### **Étape 4 : Solutions Alternatives**

#### **Option A : Mode Tunnel**
```bash
cd react-native
npx expo start --tunnel --clear
```

#### **Option B : Mode LAN avec IP fixe**
```bash
cd react-native
npx expo start --lan --clear
```

#### **Option C : Version Web Pure**
```bash
cd react-native
npx expo start --web --port 8082
# Puis ouvrez http://[VOTRE_IP]:8082 sur mobile
```

---

## 📱 Configuration pour Mobile

### **Fichiers Créés :**
1. **`start-expo-tunnel.bat`** - Démarrage en mode tunnel
2. **`start-web-mobile.bat`** - Version web mobile
3. **`AuthAppSimple.js`** - Version mobile simplifiée

### **Modification Nécessaire :**
```javascript
// Dans AuthAppSimple.js, ligne 13 :
const API_BASE_URL = 'http://*************:4000';
// Remplacez ************* par l'IP de VOTRE PC
```

### **Comment Trouver Votre IP :**
1. **Windows :** Ouvrez `cmd` et tapez `ipconfig`
2. **Cherchez :** "Carte réseau sans fil Wi-Fi"
3. **Notez :** L'adresse IPv4 (ex: *************)

---

## 🎯 Méthode Recommandée

### **Pour Développement Rapide :**
```bash
# 1. Utilisez la version web mobile
start-web-mobile.bat

# 2. Ouvrez sur mobile : http://[VOTRE_IP]:8082
```

### **Pour Test Complet :**
```bash
# 1. Utilisez le mode tunnel
start-expo-tunnel.bat

# 2. Scannez le QR code avec Expo Go
```

### **Pour Production :**
```bash
# 1. Utilisez la version simplifiée
# Modifiez package.json : "main": "AuthAppSimple.js"
# 2. Démarrez en tunnel
npx expo start --tunnel
```

---

## 🔍 Diagnostic des Erreurs

### **Si "Something went wrong" persiste :**
1. **Vérifiez** la connexion réseau
2. **Redémarrez** Expo Go
3. **Utilisez** le mode tunnel
4. **Testez** la version web

### **Si la connexion au serveur échoue :**
1. **Vérifiez** que le serveur d'auth fonctionne
2. **Modifiez** l'IP dans le code
3. **Testez** l'URL dans le navigateur mobile

### **Si l'interface ne s'affiche pas :**
1. **Utilisez** `AuthAppSimple.js`
2. **Nettoyez** le cache : `--clear`
3. **Redémarrez** l'application

---

**✅ Avec ces solutions, votre application devrait fonctionner sur mobile !**

Commencez par la **Solution 2 (Version Web Mobile)** qui est la plus simple et fiable.
