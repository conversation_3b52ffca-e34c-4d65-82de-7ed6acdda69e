import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
} from 'react-native';

const API_BASE_URL = 'http://localhost:4000';

export default function AuthApp() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Erreur', 'Veuillez saisir votre email et mot de passe');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion:', { email });

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          password: password
        }),
      });

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success) {
        Alert.alert(
          'Connexion réussie',
          `Bienvenue ${data.user.prenom} ${data.user.nom}!\nRôle: ${data.user.role}\nRedirection: ${data.redirectTo}`,
          [
            {
              text: 'OK',
              onPress: () => {
                console.log(`✅ Connexion réussie - Redirection: ${data.redirectTo}`);
                // Ici vous pouvez ajouter la logique de redirection
              }
            }
          ]
        );
      } else {
        Alert.alert('Erreur de connexion', data.message || 'Erreur de connexion');
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      Alert.alert(
        'Erreur de connexion',
        'Impossible de se connecter au serveur. Vérifiez que le serveur d\'authentification est démarré sur le port 4000.'
      );
    } finally {
      setLoading(false);
    }
  };

  const fillTestData = (userType) => {
    if (userType === 'admin') {
      setEmail('<EMAIL>');
      setPassword('Admin123');
    } else if (userType === 'tech') {
      setEmail('<EMAIL>');
      setPassword('Tech123');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          {/* Logo et titre */}
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <Text style={styles.logoIcon}>💧</Text>
            </View>
            <Text style={styles.logoTitle}>AquaTrack</Text>
            <Text style={styles.logoSubtitle}>Système de Facturation Mobile</Text>
          </View>

          {/* Formulaire de connexion */}
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Connexion</Text>
            
            {/* Champ Email */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputIcon}>📧</Text>
              <TextInput
                style={styles.input}
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
              />
            </View>

            {/* Champ Mot de passe */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputIcon}>🔒</Text>
              <TextInput
                style={styles.input}
                placeholder="Mot de passe"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Text style={styles.eyeText}>{showPassword ? '👁️' : '🙈'}</Text>
              </TouchableOpacity>
            </View>

            {/* Bouton de connexion */}
            <TouchableOpacity
              style={[styles.loginButton, loading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.loginButtonText}>Se connecter</Text>
              )}
            </TouchableOpacity>

            {/* Boutons de test rapide */}
            <View style={styles.testButtonsContainer}>
              <Text style={styles.testTitle}>Tests rapides :</Text>
              <View style={styles.testButtonsRow}>
                <TouchableOpacity
                  style={[styles.testButton, { backgroundColor: '#2196F3' }]}
                  onPress={() => fillTestData('admin')}
                  disabled={loading}
                >
                  <Text style={styles.testButtonText}>Admin</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.testButton, { backgroundColor: '#4CAF50' }]}
                  onPress={() => fillTestData('tech')}
                  disabled={loading}
                >
                  <Text style={styles.testButtonText}>Tech</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Informations de test */}
            <View style={styles.testInfoContainer}>
              <Text style={styles.testInfoTitle}>Comptes de test :</Text>
              <Text style={styles.testInfoText}>Admin: <EMAIL> / Admin123</Text>
              <Text style={styles.testInfoText}>Tech: <EMAIL> / Tech123</Text>
              <Text style={styles.testInfoNote}>
                Admin → Dashboard | Tech → TechnicianDashboard
              </Text>
            </View>

            {/* Informations de la base de données */}
            <View style={styles.dbInfoContainer}>
              <Text style={styles.dbInfoTitle}>📊 Base de données :</Text>
              <Text style={styles.dbInfoText}>• Base : Facturation</Text>
              <Text style={styles.dbInfoText}>• Table : utilisateur</Text>
              <Text style={styles.dbInfoText}>• Serveur : localhost:4000</Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 30,
    paddingVertical: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  logoIcon: {
    fontSize: 50,
  },
  logoTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 5,
  },
  logoSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  formTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 25,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    marginBottom: 15,
    paddingHorizontal: 15,
    backgroundColor: '#f9f9f9',
  },
  inputIcon: {
    fontSize: 18,
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 45,
    fontSize: 16,
    color: '#333',
  },
  eyeIcon: {
    padding: 5,
  },
  eyeText: {
    fontSize: 18,
  },
  loginButton: {
    backgroundColor: '#2196F3',
    borderRadius: 10,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  loginButtonDisabled: {
    backgroundColor: '#ccc',
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  testButtonsContainer: {
    marginBottom: 20,
  },
  testTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  testButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  testButton: {
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 80,
    alignItems: 'center',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  testInfoContainer: {
    backgroundColor: '#e3f2fd',
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
    marginBottom: 15,
  },
  testInfoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 5,
  },
  testInfoText: {
    fontSize: 12,
    color: '#1976d2',
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  testInfoNote: {
    fontSize: 11,
    color: '#1976d2',
    fontStyle: 'italic',
    marginTop: 5,
  },
  dbInfoContainer: {
    backgroundColor: '#f3e5f5',
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#9C27B0',
  },
  dbInfoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#7B1FA2',
    marginBottom: 5,
  },
  dbInfoText: {
    fontSize: 12,
    color: '#7B1FA2',
    marginBottom: 2,
  },
});
