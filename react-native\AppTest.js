import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Application de test simple
export default function AppTest() {
  const handleTestAuth = () => {
    Alert.alert('Test', 'Authentification en cours de développement...');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      
      <View style={styles.content}>
        {/* Logo et titre */}
        <View style={styles.logoContainer}>
          <View style={styles.logoCircle}>
            <Ionicons name="water" size={60} color="#2196F3" />
          </View>
          <Text style={styles.logoTitle}>AquaTrack</Text>
          <Text style={styles.logoSubtitle}>Système de Facturation Mobile</Text>
        </View>

        {/* Message de test */}
        <View style={styles.testContainer}>
          <Text style={styles.testTitle}>🚀 Application en cours de test</Text>
          <Text style={styles.testText}>
            L'authentification est en cours d'intégration.
          </Text>
          
          <TouchableOpacity style={styles.testButton} onPress={handleTestAuth}>
            <Ionicons name="log-in-outline" size={20} color="#fff" />
            <Text style={styles.testButtonText}>Tester l'authentification</Text>
          </TouchableOpacity>
        </View>

        {/* Informations de développement */}
        <View style={styles.devInfo}>
          <Text style={styles.devTitle}>ℹ️ Informations de développement</Text>
          <Text style={styles.devText}>• Serveur d'auth : http://localhost:4000</Text>
          <Text style={styles.devText}>• Application : http://localhost:8082</Text>
          <Text style={styles.devText}>• Status : En développement</Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  logoCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  logoTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 8,
  },
  logoSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  testContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 30,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    alignItems: 'center',
    width: '100%',
  },
  testTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  testText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  testButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 10,
    paddingVertical: 15,
    paddingHorizontal: 25,
    flexDirection: 'row',
    alignItems: 'center',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  devInfo: {
    backgroundColor: '#e3f2fd',
    borderRadius: 15,
    padding: 20,
    width: '100%',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  devTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 10,
  },
  devText: {
    fontSize: 14,
    color: '#1976d2',
    marginBottom: 5,
    fontFamily: 'monospace',
  },
});
