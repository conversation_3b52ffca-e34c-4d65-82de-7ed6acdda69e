const bcrypt = require('bcrypt');
const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

// Fonction pour créer les utilisateurs de test avec mots de passe hachés
async function createTestUsers() {
  console.log('🔐 Création des utilisateurs de test avec mots de passe hachés...');
  
  try {
    // Suppression des utilisateurs de test existants
    await pool.query(`
      DELETE FROM utilisateur WHERE email IN (
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      )
    `);
    console.log('🗑️ Utilisateurs de test existants supprimés');

    // Hachage des mots de passe
    const saltRounds = 10;
    const passwords = {
      admin: await bcrypt.hash('Admin123', saltRounds),
      tech: await bcrypt.hash('Tech123', saltRounds),
      ahmed: await bcrypt.hash('Ahmed123', saltRounds),
      fatima: await bcrypt.hash('Fatima123', saltRounds),
      mohamed: await bcrypt.hash('Mohamed123', saltRounds)
    };

    // Utilisateurs de test à créer
    const testUsers = [
      {
        nom: 'Gharbi',
        prenom: 'Fatima',
        adresse: '123 Avenue Habib Bourguiba, Tunis',
        tel: '+216 71 123 456',
        email: '<EMAIL>',
        password: passwords.admin,
        role: 'Admin',
        is_protected: true
      },
      {
        nom: 'Benali',
        prenom: 'Ahmed',
        adresse: '456 Rue de la République, Sfax',
        tel: '+216 74 987 654',
        email: '<EMAIL>',
        password: passwords.tech,
        role: 'Tech',
        is_protected: true
      },
      {
        nom: 'Benali',
        prenom: 'Ahmed',
        adresse: '789 Boulevard du 14 Janvier, Sousse',
        tel: '+216 73 555 123',
        email: '<EMAIL>',
        password: passwords.ahmed,
        role: 'Tech',
        is_protected: false
      },
      {
        nom: 'Gharbi',
        prenom: 'Fatima',
        adresse: '321 Avenue de la Liberté, Monastir',
        tel: '+216 73 444 789',
        email: '<EMAIL>',
        password: passwords.fatima,
        role: 'Admin',
        is_protected: false
      },
      {
        nom: 'Salem',
        prenom: 'Mohamed',
        adresse: '654 Rue des Martyrs, Gabès',
        tel: '+216 75 333 456',
        email: '<EMAIL>',
        password: passwords.mohamed,
        role: 'Tech',
        is_protected: false
      }
    ];

    // Insertion des utilisateurs
    for (const user of testUsers) {
      const query = `
        INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING idtech, nom, prenom, email, role
      `;
      
      const values = [
        user.nom,
        user.prenom,
        user.adresse,
        user.tel,
        user.email,
        user.password,
        user.role,
        user.is_protected
      ];

      const result = await pool.query(query, values);
      const createdUser = result.rows[0];
      
      console.log(`✅ Utilisateur créé: ${createdUser.prenom} ${createdUser.nom} (${createdUser.email}) - ${createdUser.role}`);
    }

    // Vérification des utilisateurs créés
    const verifyQuery = `
      SELECT 
        idtech,
        nom,
        prenom,
        email,
        role,
        is_protected,
        created_at
      FROM utilisateur 
      ORDER BY role DESC, nom, prenom
    `;
    
    const verifyResult = await pool.query(verifyQuery);
    
    console.log('\n📊 Utilisateurs dans la base de données:');
    console.log('=====================================');
    verifyResult.rows.forEach(user => {
      console.log(`ID: ${user.idtech} | ${user.prenom} ${user.nom} | ${user.email} | ${user.role} | Protégé: ${user.is_protected}`);
    });

    // Statistiques
    const statsQuery = `
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN role = 'Admin' THEN 1 END) as admin_count,
        COUNT(CASE WHEN role = 'Tech' THEN 1 END) as tech_count
      FROM utilisateur
    `;
    
    const statsResult = await pool.query(statsQuery);
    const stats = statsResult.rows[0];
    
    console.log('\n📈 Statistiques:');
    console.log(`Total utilisateurs: ${stats.total_users}`);
    console.log(`Administrateurs: ${stats.admin_count}`);
    console.log(`Techniciens: ${stats.tech_count}`);

    console.log('\n🎯 Comptes de test disponibles:');
    console.log('================================');
    console.log('👨‍💼 Admin: <EMAIL> / Admin123');
    console.log('👨‍🔧 Tech: <EMAIL> / Tech123');
    console.log('👨‍🔧 Ahmed: <EMAIL> / Ahmed123');
    console.log('👩‍💼 Fatima: <EMAIL> / Fatima123');
    console.log('👨‍🔧 Mohamed: <EMAIL> / Mohamed123');

    console.log('\n✅ Création des utilisateurs de test terminée avec succès!');

  } catch (error) {
    console.error('❌ Erreur lors de la création des utilisateurs de test:', error);
  } finally {
    await pool.end();
  }
}

// Exécution du script
if (require.main === module) {
  createTestUsers();
}

module.exports = { createTestUsers };
