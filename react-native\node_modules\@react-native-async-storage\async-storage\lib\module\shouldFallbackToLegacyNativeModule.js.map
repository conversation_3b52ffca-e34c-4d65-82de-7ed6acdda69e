{"version": 3, "names": ["NativeModules", "shouldFallbackToLegacyNativeModule", "_NativeModules$Native", "expoConstants", "modulesConstants", "ExponentConstants", "isLegacySdkVersion", "appOwnership", "executionEnvironment", "includes"], "sources": ["shouldFallbackToLegacyNativeModule.ts"], "sourcesContent": ["import { NativeModules } from \"react-native\";\n\nexport function shouldFallbackToLegacyNativeModule(): boolean {\n  const expoConstants =\n    NativeModules[\"NativeUnimoduleProxy\"]?.modulesConstants?.ExponentConstants;\n\n  if (expoConstants) {\n    /**\n     * In SDK <= 39, appOwnership is defined in managed apps but executionEnvironment is not.\n     * In bare React Native apps using expo-constants, appOwnership is never defined, so\n     * isLegacySdkVersion will be false in that context.\n     */\n    const isLegacySdkVersion =\n      expoConstants.appOwnership && !expoConstants.executionEnvironment;\n\n    /**\n     * Expo managed apps don't include the @react-native-async-storage/async-storage\n     * native modules yet, but the API interface is the same, so we can use the version\n     * exported from React Native still.\n     *\n     * If in future releases (eg: @react-native-async-storage/async-storage >= 2.0.0) this\n     * will likely not be valid anymore, and the package will need to be included in the Expo SDK\n     * to continue to work.\n     */\n    if (\n      isLegacySdkVersion ||\n      [\"storeClient\", \"standalone\"].includes(expoConstants.executionEnvironment)\n    ) {\n      return true;\n    }\n  }\n\n  return false;\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,cAAc;AAE5C,OAAO,SAASC,kCAAkCA,CAAA,EAAY;EAAA,IAAAC,qBAAA;EAC5D,MAAMC,aAAa,IAAAD,qBAAA,GACjBF,aAAa,CAAC,sBAAsB,CAAC,cAAAE,qBAAA,gBAAAA,qBAAA,GAArCA,qBAAA,CAAuCE,gBAAgB,cAAAF,qBAAA,uBAAvDA,qBAAA,CAAyDG,iBAAiB;EAE5E,IAAIF,aAAa,EAAE;IACjB;AACJ;AACA;AACA;AACA;IACI,MAAMG,kBAAkB,GACtBH,aAAa,CAACI,YAAY,IAAI,CAACJ,aAAa,CAACK,oBAAoB;;IAEnE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IACEF,kBAAkB,IAClB,CAAC,aAAa,EAAE,YAAY,CAAC,CAACG,QAAQ,CAACN,aAAa,CAACK,oBAAoB,CAAC,EAC1E;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd"}