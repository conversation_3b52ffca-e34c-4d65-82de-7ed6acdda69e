import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          backgroundColor: '#f5f7fa',
          color: '#333',
          fontFamily: 'Arial, sans-serif'
        }}>
          <div style={{
            backgroundColor: '#fff',
            padding: '40px',
            borderRadius: '20px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            textAlign: 'center',
            maxWidth: '500px'
          }}>
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: '#2196F3',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 20px',
              fontSize: '40px',
              color: 'white'
            }}>
              💧
            </div>
            
            <h1 style={{
              color: '#2196F3',
              fontSize: '32px',
              marginBottom: '10px',
              fontWeight: 'bold'
            }}>
              AquaTrack
            </h1>
            
            <p style={{
              fontSize: '18px',
              color: '#666',
              marginBottom: '30px'
            }}>
              Système de Facturation Mobile
            </p>
            
            <div style={{
              backgroundColor: '#e3f2fd',
              padding: '20px',
              borderRadius: '10px',
              marginBottom: '20px',
              borderLeft: '4px solid #2196F3'
            }}>
              <h3 style={{ color: '#1976d2', marginBottom: '15px' }}>
                📱 Application Mobile React Native
              </h3>
              <p style={{ color: '#1976d2', marginBottom: '10px' }}>
                Cette application est conçue pour fonctionner sur mobile avec React Native.
              </p>
              <p style={{ color: '#1976d2', fontSize: '14px' }}>
                <strong>Pour accéder à l'application :</strong>
              </p>
              <ol style={{ 
                color: '#1976d2', 
                fontSize: '14px', 
                textAlign: 'left',
                paddingLeft: '20px'
              }}>
                <li>Installez <strong>Expo Go</strong> sur votre téléphone</li>
                <li>Scannez le QR code affiché dans le terminal</li>
                <li>Ou ouvrez : <code>http://localhost:8081</code></li>
              </ol>
            </div>
            
            <div style={{
              backgroundColor: '#e8f5e8',
              padding: '15px',
              borderRadius: '10px',
              borderLeft: '4px solid #4caf50'
            }}>
              <h4 style={{ color: '#2e7d32', marginBottom: '10px' }}>
                ✅ Fonctionnalités Disponibles
              </h4>
              <p style={{ color: '#2e7d32', fontSize: '14px' }}>
                • Gestion des clients<br/>
                • Suivi des consommations<br/>
                • Génération de factures<br/>
                • Scanner QR codes<br/>
                • Cartes géographiques
              </p>
            </div>
            
            <div style={{ marginTop: '30px' }}>
              <a 
                href="http://localhost:8081" 
                target="_blank" 
                rel="noopener noreferrer"
                style={{
                  backgroundColor: '#2196F3',
                  color: 'white',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  textDecoration: 'none',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  display: 'inline-block',
                  marginRight: '10px'
                }}
              >
                🌐 Ouvrir dans le navigateur
              </a>
              
              <button 
                onClick={() => window.location.reload()}
                style={{
                  backgroundColor: '#4caf50',
                  color: 'white',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  border: 'none',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}
              >
                🔄 Actualiser
              </button>
            </div>
          </div>
          
          <div style={{
            marginTop: '20px',
            fontSize: '12px',
            color: '#999'
          }}>
            © 2024 AquaTrack Solutions - Application React Native
          </div>
        </div>
      </header>
    </div>
  );
}

export default App;
