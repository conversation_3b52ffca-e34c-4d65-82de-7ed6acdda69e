{"version": 3, "names": [], "sources": ["types.ts"], "sourcesContent": ["export type ErrorLike = {\n  message: string;\n  key?: string;\n};\n\nexport type Callback = (error?: Error | null) => void;\n\nexport type CallbackWithResult<T> = (\n  error?: Error | null,\n  result?: T | null\n) => void;\n\nexport type KeyValuePair = [string, string | null];\n\nexport type MultiCallback = (errors?: readonly (Error | null)[] | null) => void;\n\nexport type MultiGetCallback = (\n  errors?: readonly (Error | null)[] | null,\n  result?: readonly KeyValuePair[]\n) => void;\n\nexport type MultiRequest = {\n  keys: readonly string[];\n  callback?: MultiGetCallback;\n  keyIndex: number;\n  resolve?: (result: readonly KeyValuePair[]) => void;\n  reject?: (error?: ErrorLike) => void;\n};\n\nexport type AsyncStorageHook = {\n  getItem: (callback?: CallbackWithResult<string>) => Promise<string | null>;\n  setItem: (value: string, callback?: Callback) => Promise<void>;\n  mergeItem: (value: string, callback?: Callback) => Promise<void>;\n  removeItem: (callback?: Callback) => Promise<void>;\n};\n\n/**\n * `AsyncStorage` is a simple, unencrypted, asynchronous, persistent, key-value\n * storage system that is global to the app.  It should be used instead of\n * LocalStorage.\n *\n * See https://react-native-async-storage.github.io/async-storage/docs/api\n */\nexport type AsyncStorageStatic = {\n  /**\n   * Fetches an item for a `key` and invokes a callback upon completion.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#getitem\n   */\n  getItem: (\n    key: string,\n    callback?: CallbackWithResult<string>\n  ) => Promise<string | null>;\n\n  /**\n   * Sets the value for a `key` and invokes a callback upon completion.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#setitem\n   */\n  setItem: (key: string, value: string, callback?: Callback) => Promise<void>;\n\n  /**\n   * Removes an item for a `key` and invokes a callback upon completion.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#removeitem\n   */\n  removeItem: (key: string, callback?: Callback) => Promise<void>;\n\n  /**\n   * Merges an existing `key` value with an input value, assuming both values\n   * are stringified JSON.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#mergeitem\n   */\n  mergeItem: (key: string, value: string, callback?: Callback) => Promise<void>;\n\n  /**\n   * Erases *all* `AsyncStorage` for all clients, libraries, etc. You probably\n   * don't want to call this; use `removeItem` or `multiRemove` to clear only\n   * your app's keys.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#clear\n   */\n  clear: (callback?: Callback) => Promise<void>;\n\n  /**\n   * Gets *all* keys known to your app; for all callers, libraries, etc.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#getallkeys\n   */\n  getAllKeys: (\n    callback?: CallbackWithResult<readonly string[]>\n  ) => Promise<readonly string[]>;\n\n  /**\n   * The following batched functions are useful for executing a lot of\n   * operations at once, allowing for native optimizations and provide the\n   * convenience of a single callback after all operations are complete.\n   *\n   * These functions return arrays of errors, potentially one for every key.\n   * For key-specific errors, the Error object will have a key property to\n   * indicate which key caused the error.\n   */\n\n  /**\n   * Flushes any pending requests using a single batch call to get the data.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#flushgetrequests\n   * */\n  flushGetRequests: () => void;\n\n  /**\n   * This allows you to batch the fetching of items given an array of `key`\n   * inputs. Your callback will be invoked with an array of corresponding\n   * key-value pairs found.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#multiget\n   */\n  multiGet: (\n    keys: readonly string[],\n    callback?: MultiGetCallback\n  ) => Promise<readonly KeyValuePair[]>;\n\n  /**\n   * Use this as a batch operation for storing multiple key-value pairs. When\n   * the operation completes you'll get a single callback with any errors.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#multiset\n   */\n  multiSet: (\n    keyValuePairs: [string, string][],\n    callback?: MultiCallback\n  ) => Promise<void>;\n\n  /**\n   * Call this to batch the deletion of all keys in the `keys` array.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#multiremove\n   */\n  multiRemove: (\n    keys: readonly string[],\n    callback?: MultiCallback\n  ) => Promise<void>;\n\n  /**\n   * Batch operation to merge in existing and new values for a given set of\n   * keys. This assumes that the values are stringified JSON.\n   *\n   * See https://react-native-async-storage.github.io/async-storage/docs/api#multimerge\n   */\n  multiMerge: (\n    keyValuePairs: [string, string][],\n    callback?: MultiCallback\n  ) => Promise<void>;\n};\n"], "mappings": ""}