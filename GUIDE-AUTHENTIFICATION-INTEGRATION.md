# 🔐 Guide d'Intégration - Authentification AquaTrack

## ✅ Intégration Terminée !

Votre système d'authentification a été **intégré avec succès** dans votre projet React Native existant.

## 📁 Fichiers Ajoutés/Modifiés

### 🆕 **Nouveaux Fichiers (5 fichiers)**
1. **`AuthenticationServer.js`** - Serveur Node.js d'authentification
2. **`react-native/screens/AuthenticationScreen.js`** - Écran de connexion mobile
3. **`react-native/screens/TechnicianDashboard.js`** - Dashboard pour les techniciens
4. **`react-native/screens/Dashboard.js`** - Dashboard pour les administrateurs
5. **`start-auth-project.bat`** - Script de démarrage complet

### 🔄 **Fichier Modifié**
- **`react-native/App.js`** - Navigation mise à jour avec authentification

## 🚀 Comment Démarrer Votre Projet

### **Option 1 : Démarrage Automatique (Recommandé)**
```bash
# Double-cliquez sur :
start-auth-project.bat
```

### **Option 2 : Démarrage Manuel**
```bash
# Terminal 1 - Serveur d'authentification
node AuthenticationServer.js

# Terminal 2 - Application React Native
cd react-native
npx expo start --web --port 8082
```

## 🔑 Comptes de Test Disponibles

| Email | Mot de passe | Rôle | Redirection |
|-------|--------------|------|-------------|
| `<EMAIL>` | `Admin123` | Admin | → `Dashboard.js` |
| `<EMAIL>` | `Tech123` | Tech | → `TechnicianDashboard.js` |

## 🎯 Flux d'Authentification

### 1. **Page de Connexion**
- L'utilisateur arrive sur `AuthenticationScreen.js`
- Saisit email/mot de passe
- Le système vérifie dans la table `utilisateur`

### 2. **Redirection Automatique**
```javascript
// Logique de redirection dans AuthenticationServer.js
if (user.role === 'Tech') {
  redirectTo = 'TechnicianDashboard';  // → TechnicianDashboard.js
} else if (user.role === 'Admin') {
  redirectTo = 'Dashboard';            // → Dashboard.js
}
```

### 3. **Navigation dans l'App**
- **Techniciens** : Accès aux fonctions terrain (clients, consommation, scanner)
- **Administrateurs** : Accès à la gestion complète du système

## 🗄️ Base de Données

### **Table Utilisée**
```sql
-- Table: utilisateur (dans la base "Facturation")
CREATE TABLE utilisateur (
    idtech SERIAL PRIMARY KEY,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    adresse VARCHAR(255),
    tel VARCHAR(20),
    email VARCHAR(100),
    password VARCHAR(100),
    role VARCHAR(10),        -- 'Tech' ou 'Admin'
    is_protected BOOLEAN
);
```

### **Configuration Serveur**
```javascript
// Dans AuthenticationServer.js
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',  // Votre base de données
  password: '123456',
  port: 5432,
});
```

## 🔧 Fonctionnalités Intégrées

### ✅ **Authentification**
- Connexion avec email/mot de passe
- Vérification dans la table `utilisateur`
- Support mots de passe en clair ET hachés (bcrypt)
- Gestion de session avec AsyncStorage

### ✅ **Redirection Intelligente**
- **Role = 'Tech'** → `TechnicianDashboard.js`
- **Role = 'Admin'** → `Dashboard.js`
- Sauvegarde automatique de la session

### ✅ **Navigation Complète**
- Accès à tous vos écrans existants :
  - `ClientsListScreen.js`
  - `ConsommationScreen.js`
  - `FacturesScreen.js`
  - `ScannerScreen.js`
  - `MapScreen.js`

### ✅ **Sécurité**
- Tokens de session
- Déconnexion sécurisée
- Vérification automatique de session

## 📱 Utilisation

### **1. Démarrer le Projet**
```bash
start-auth-project.bat
```

### **2. Ouvrir l'Application**
- URL : http://localhost:8082
- Se connecter avec un compte de test

### **3. Tester les Rôles**
- **Technicien** : Interface verte, fonctions terrain
- **Admin** : Interface bleue, gestion complète

### **4. Navigation**
- Chaque dashboard donne accès aux écrans appropriés
- Déconnexion disponible dans chaque dashboard

## 🔄 Personnalisation

### **Ajouter des Utilisateurs**
```sql
-- Dans votre base "Facturation"
INSERT INTO utilisateur (nom, prenom, email, password, role, is_protected)
VALUES ('Nouveau', 'Utilisateur', '<EMAIL>', 'motdepasse', 'Tech', false);
```

### **Modifier les Redirections**
```javascript
// Dans AuthenticationServer.js, ligne ~95
if (user.role === 'Tech') {
  redirectTo = 'VotrePageTech';  // Changez ici
} else if (user.role === 'Admin') {
  redirectTo = 'VotrePageAdmin'; // Changez ici
}
```

## 🎯 Prochaines Étapes

1. **Testez** l'authentification avec les comptes fournis
2. **Créez** vos propres utilisateurs dans la base
3. **Personnalisez** les dashboards selon vos besoins
4. **Intégrez** vos fonctionnalités métier existantes

---

**✅ Votre système d'authentification est maintenant intégré et fonctionnel !**

Votre application React Native utilise maintenant votre table `utilisateur` avec redirection automatique selon le rôle.
