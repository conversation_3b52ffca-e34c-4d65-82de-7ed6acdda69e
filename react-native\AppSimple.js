import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function AppSimple() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>🚀 AquaTrack Mobile</Text>
      <Text style={styles.subtitle}>Application en cours de développement</Text>
      <Text style={styles.status}>✅ React Native fonctionne !</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  status: {
    fontSize: 18,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
});
