@echo off
echo ========================================
echo   Démarrage du Backend d'Authentification
echo ========================================
echo.

cd auth-backend

echo 1. Vérification des dépendances...
if not exist node_modules (
    echo Installation des dépendances...
    npm install
    echo.
)

echo 2. Création des utilisateurs de test...
node create-test-users.js
echo.

echo 3. Démarrage du serveur...
echo.
echo Serveur d'authentification AquaTrack
echo Port: 4000
echo Base de données: Facturation
echo.
echo Endpoints disponibles:
echo   POST http://localhost:4000/api/auth/login
echo   POST http://localhost:4000/api/auth/register
echo   GET  http://localhost:4000/api/auth/profile
echo   GET  http://localhost:4000/api/auth/users
echo.

npm start

pause
