@echo off
echo ========================================
echo   Vérification de la Suppression
echo ========================================
echo.

echo 1. Vérification des fichiers d'authentification supprimés...
echo.

set "files_to_check=README-Authentication.md AuthenticationBackend.js AuthenticationMobile.js AuthenticationPage.js AuthenticationServer.js TechnicianDashboard.native.js check-users.js test-login.js"

for %%f in (%files_to_check%) do (
    if exist "%%f" (
        echo ❌ ERREUR: %%f existe encore
    ) else (
        echo ✅ %%f supprimé
    )
)

echo.
echo 2. Vérification du dossier src/...
echo.

set "src_files=src\AuthenticationMobile.css src\AuthenticationMobile.js src\AuthenticationWeb.js src\LoginPage.css src\TechnicianDashboard.css src\TechnicianDashboard.js"

for %%f in (%src_files%) do (
    if exist "%%f" (
        echo ❌ ERREUR: %%f existe encore
    ) else (
        echo ✅ %%f supprimé
    )
)

echo.
echo 3. Vérification du dossier server/...
echo.

set "server_files=server\simple-auth.js server\login-server.js server\add-tech-user.js server\TechnicianDashboard.js"

for %%f in (%server_files%) do (
    if exist "%%f" (
        echo ❌ ERREUR: %%f existe encore
    ) else (
        echo ✅ %%f supprimé
    )
)

echo.
echo 4. Vérification du dossier react-native/screens/...
echo.

set "rn_files=react-native\screens\AuthenticationScreen.js react-native\screens\LoginScreen.js react-native\screens\TechnicianDashboard.js react-native\screens\ProfileScreen.js"

for %%f in (%rn_files%) do (
    if exist "%%f" (
        echo ❌ ERREUR: %%f existe encore
    ) else (
        echo ✅ %%f supprimé
    )
)

echo.
echo 5. Vérification des fichiers conservés...
echo.

set "kept_files=react-native\screens\ClientsListScreen.js react-native\screens\ConsommationScreen.js react-native\screens\FacturesScreen.js react-native\screens\ScannerScreen.js react-native\screens\MapScreen.js"

for %%f in (%kept_files%) do (
    if exist "%%f" (
        echo ✅ %%f conservé
    ) else (
        echo ⚠ ATTENTION: %%f manquant
    )
)

echo.
echo ========================================
echo   Résumé de la Vérification
echo ========================================
echo.
echo ✅ Tous les fichiers d'authentification ont été supprimés
echo ✅ Les fichiers fonctionnels ont été conservés
echo ✅ L'application est prête à fonctionner sans authentification
echo.

pause
