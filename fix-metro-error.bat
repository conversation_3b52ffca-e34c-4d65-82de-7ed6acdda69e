@echo off
echo ========================================
echo   Correction Erreur Metro/Compilation
echo ========================================
echo.

cd react-native

echo 1. Arrêt de tous les processus Expo/Metro...
taskkill /f /im node.exe 2>nul
taskkill /f /im expo.exe 2>nul

echo 2. Nettoyage du cache Metro...
call npx expo start --clear

echo 3. Suppression des caches...
if exist .expo rmdir /s /q .expo
if exist node_modules\.cache rmdir /s /q node_modules\.cache

echo 4. Redémarrage avec configuration simplifiée...
call npx expo start --tunnel --reset-cache

echo.
echo ========================================
echo   Processus de correction terminé
echo ========================================
echo.
echo Instructions:
echo 1. Attendez que le QR code apparaisse
echo 2. Scannez le nouveau QR code
echo 3. Si erreur persiste, utilisez la Solution 2
echo.

pause
