const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configuration simplifiée pour éviter les erreurs de transformation
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    keep_fnames: true,
    mangle: {
      keep_fnames: true,
    },
  },
};

// Résolution des modules simplifiée
config.resolver = {
  ...config.resolver,
  alias: {
    // Éviter les conflits de dépendances
  },
};

module.exports = config;
