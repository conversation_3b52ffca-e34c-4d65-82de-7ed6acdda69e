import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  ScrollView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const Dashboard = ({ navigation, route }) => {
  const [user, setUser] = useState(route.params?.user || null);

  useEffect(() => {
    if (!user) {
      loadUserData();
    }
  }, []);

  const loadUserData = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        setUser(JSON.parse(userData));
      } else {
        navigation.replace('AuthenticationScreen');
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des données utilisateur:', error);
      navigation.replace('AuthenticationScreen');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        {
          text: 'Annuler',
          style: 'cancel',
        },
        {
          text: 'Déconnexion',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.multiRemove(['userData', 'authToken', 'loginTime']);
              console.log('👋 Déconnexion réussie');
              navigation.replace('AuthenticationScreen');
            } catch (error) {
              console.error('❌ Erreur lors de la déconnexion:', error);
            }
          },
        },
      ]
    );
  };

  const navigateToScreen = (screenName) => {
    if (navigation.navigate) {
      navigation.navigate(screenName);
    } else {
      Alert.alert('Navigation', `Redirection vers ${screenName}`);
    }
  };

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Chargement...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      
      {/* En-tête Admin */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.userInfo}>
            <View style={styles.avatarContainer}>
              <Ionicons name="shield" size={30} color="#fff" />
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{user.prenom} {user.nom}</Text>
              <Text style={styles.userRole}>Administrateur</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Ionicons name="log-out-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content}>
        {/* Carte de bienvenue */}
        <View style={styles.welcomeCard}>
          <Text style={styles.welcomeTitle}>Tableau de Bord Administrateur</Text>
          <Text style={styles.welcomeText}>
            Bienvenue {user.prenom} ! Gérez l'ensemble du système AquaTrack.
          </Text>
        </View>

        {/* Actions administrateur */}
        <View style={styles.actionsCard}>
          <Text style={styles.cardTitle}>Gestion du système</Text>
          
          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={() => navigateToScreen('ClientsList')}
          >
            <Ionicons name="people-outline" size={24} color="#4CAF50" />
            <Text style={styles.actionText}>Gestion des Clients</Text>
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={() => navigateToScreen('Consommation')}
          >
            <Ionicons name="water-outline" size={24} color="#2196F3" />
            <Text style={styles.actionText}>Gestion Consommations</Text>
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={() => navigateToScreen('Factures')}
          >
            <Ionicons name="document-text-outline" size={24} color="#FF9800" />
            <Text style={styles.actionText}>Gestion des Factures</Text>
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={() => Alert.alert('Info', 'Gestion des utilisateurs (à implémenter)')}
          >
            <Ionicons name="settings-outline" size={24} color="#9C27B0" />
            <Text style={styles.actionText}>Gestion des Utilisateurs</Text>
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={() => navigateToScreen('Map')}
          >
            <Ionicons name="map-outline" size={24} color="#F44336" />
            <Text style={styles.actionText}>Vue Géographique</Text>
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          </TouchableOpacity>
        </View>

        {/* Statistiques administrateur */}
        <View style={styles.statsCard}>
          <Text style={styles.cardTitle}>Statistiques générales</Text>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Ionicons name="people" size={30} color="#4CAF50" />
              <Text style={styles.statNumber}>-</Text>
              <Text style={styles.statLabel}>Clients</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="water" size={30} color="#2196F3" />
              <Text style={styles.statNumber}>-</Text>
              <Text style={styles.statLabel}>Consommations</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="document-text" size={30} color="#FF9800" />
              <Text style={styles.statNumber}>-</Text>
              <Text style={styles.statLabel}>Factures</Text>
            </View>
          </View>
        </View>

        {/* Informations utilisateur */}
        <View style={styles.infoCard}>
          <View style={styles.cardHeader}>
            <Ionicons name="person-circle-outline" size={24} color="#2196F3" />
            <Text style={styles.cardTitle}>Mes informations</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Email :</Text>
            <Text style={styles.infoValue}>{user.email}</Text>
          </View>
          {user.tel && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Téléphone :</Text>
              <Text style={styles.infoValue}>{user.tel}</Text>
            </View>
          )}
          {user.adresse && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Adresse :</Text>
              <Text style={styles.infoValue}>{user.adresse}</Text>
            </View>
          )}
        </View>

        {/* Actions rapides */}
        <View style={styles.quickActionsCard}>
          <Text style={styles.cardTitle}>Actions rapides</Text>
          <View style={styles.quickActionsRow}>
            <TouchableOpacity 
              style={styles.quickActionButton}
              onPress={() => navigateToScreen('Scanner')}
            >
              <Ionicons name="qr-code" size={30} color="#fff" />
              <Text style={styles.quickActionText}>Scanner</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.quickActionButton, { backgroundColor: '#4CAF50' }]}
              onPress={() => Alert.alert('Info', 'Rapport mensuel (à implémenter)')}
            >
              <Ionicons name="bar-chart" size={30} color="#fff" />
              <Text style={styles.quickActionText}>Rapports</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.quickActionButton, { backgroundColor: '#FF9800' }]}
              onPress={() => Alert.alert('Info', 'Paramètres système (à implémenter)')}
            >
              <Ionicons name="settings" size={30} color="#fff" />
              <Text style={styles.quickActionText}>Paramètres</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: '#2196F3',
    paddingTop: 10,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  userRole: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
  },
  logoutButton: {
    padding: 10,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  welcomeCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  welcomeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  welcomeText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  actionsCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 10,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  actionText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 15,
    flex: 1,
  },
  statsCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 15,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoLabel: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 16,
    color: '#333',
    flex: 1,
    textAlign: 'right',
  },
  quickActionsCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  quickActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 15,
  },
  quickActionButton: {
    backgroundColor: '#2196F3',
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
    minWidth: 80,
  },
  quickActionText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 8,
  },
});

export default Dashboard;
