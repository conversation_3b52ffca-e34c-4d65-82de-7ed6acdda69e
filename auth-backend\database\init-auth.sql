-- Script d'initialisation de la base de données d'authentification AquaTrack
-- Base de données: Facturation

-- Création de la table utilisateur (si elle n'existe pas déjà)
CREATE TABLE IF NOT EXISTS utilisateur (
    idtech SERIAL PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    adresse VARCHAR(255),
    tel VARCHAR(20),
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL,
    role VARCHAR(10) DEFAULT 'Tech' CHECK (role IN ('Admin', 'Tech')),
    is_protected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_utilisateur_email ON utilisateur(email);
CREATE INDEX IF NOT EXISTS idx_utilisateur_role ON utilisateur(role);

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger pour mettre à jour automatiquement updated_at
DROP TRIGGER IF EXISTS update_utilisateur_updated_at ON utilisateur;
CREATE TRIGGER update_utilisateur_updated_at
    BEFORE UPDATE ON utilisateur
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Commentaires sur la table et les colonnes
COMMENT ON TABLE utilisateur IS 'Table des utilisateurs du système AquaTrack';
COMMENT ON COLUMN utilisateur.idtech IS 'Identifiant unique du technicien/utilisateur';
COMMENT ON COLUMN utilisateur.nom IS 'Nom de famille de l\'utilisateur';
COMMENT ON COLUMN utilisateur.prenom IS 'Prénom de l\'utilisateur';
COMMENT ON COLUMN utilisateur.adresse IS 'Adresse complète de l\'utilisateur';
COMMENT ON COLUMN utilisateur.tel IS 'Numéro de téléphone de l\'utilisateur';
COMMENT ON COLUMN utilisateur.email IS 'Adresse email unique pour la connexion';
COMMENT ON COLUMN utilisateur.password IS 'Mot de passe haché avec bcrypt';
COMMENT ON COLUMN utilisateur.role IS 'Rôle de l\'utilisateur: Admin ou Tech';
COMMENT ON COLUMN utilisateur.is_protected IS 'Indique si le compte est protégé contre la suppression';
COMMENT ON COLUMN utilisateur.created_at IS 'Date et heure de création du compte';
COMMENT ON COLUMN utilisateur.updated_at IS 'Date et heure de dernière modification';

-- Affichage de confirmation
SELECT 'Table utilisateur créée avec succès!' as message;
