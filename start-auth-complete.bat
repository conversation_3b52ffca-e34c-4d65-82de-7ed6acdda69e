@echo off
echo ========================================
echo   Démarrage Complet - AquaTrack Auth
echo ========================================
echo.

echo 1. Démarrage du serveur d'authentification...
start "Serveur Auth" cmd /k "node AuthenticationServer.js"

echo 2. Attente du démarrage du serveur...
timeout /t 3 /nobreak >nul

echo 3. Démarrage de l'application React Native...
cd react-native
start "App Auth" cmd /k "npx expo start --web --port 8082 --offline --clear"

echo.
echo ========================================
echo   Système démarré avec succès !
echo ========================================
echo.
echo 🔗 URLs importantes:
echo   • Serveur d'auth: http://localhost:4000
echo   • Application: http://localhost:8082
echo.
echo 🔑 Comptes de test:
echo   • Admin: <EMAIL> / Admin123
echo   • Tech: <EMAIL> / Tech123
echo.
echo 📊 Base de données:
echo   • Base: Facturation
echo   • Table: utilisateur
echo   • Serveur: localhost:5432
echo.
echo 📝 Instructions:
echo   1. Ouvrez http://localhost:8082
echo   2. Utilisez les boutons "Admin" ou "Tech" pour remplir automatiquement
echo   3. Cliquez sur "Se connecter"
echo   4. Vérifiez la redirection selon le rôle
echo.

pause
