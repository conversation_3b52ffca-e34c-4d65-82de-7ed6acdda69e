const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:4000';

// Fonction pour tester la connexion
async function testLogin(email, password) {
  try {
    console.log(`🔐 Test de connexion pour: ${email}`);
    
    const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();
    
    if (data.success) {
      console.log(`✅ Connexion réussie pour ${data.user.prenom} ${data.user.nom} (${data.user.role})`);
      return data.token;
    } else {
      console.log(`❌ Échec de la connexion: ${data.message}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ Erreur de connexion: ${error.message}`);
    return null;
  }
}

// Fonction pour tester l'inscription
async function testRegister(userData) {
  try {
    console.log(`📝 Test d'inscription pour: ${userData.email}`);
    
    const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    const data = await response.json();
    
    if (data.success) {
      console.log(`✅ Inscription réussie pour ${data.user.prenom} ${data.user.nom}`);
      return data.token;
    } else {
      console.log(`❌ Échec de l'inscription: ${data.message}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ Erreur d'inscription: ${error.message}`);
    return null;
  }
}

// Fonction pour tester la récupération du profil
async function testProfile(token) {
  try {
    console.log('👤 Test de récupération du profil...');
    
    const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    
    if (data.success) {
      console.log(`✅ Profil récupéré: ${data.user.prenom} ${data.user.nom}`);
      return true;
    } else {
      console.log(`❌ Échec de la récupération du profil: ${data.message}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Erreur de récupération du profil: ${error.message}`);
    return false;
  }
}

// Fonction pour tester la liste des utilisateurs (Admin seulement)
async function testUsersList(token) {
  try {
    console.log('👥 Test de récupération de la liste des utilisateurs...');
    
    const response = await fetch(`${API_BASE_URL}/api/auth/users`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    
    if (data.success) {
      console.log(`✅ Liste des utilisateurs récupérée: ${data.count} utilisateurs`);
      return true;
    } else {
      console.log(`❌ Échec de la récupération de la liste: ${data.message}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Erreur de récupération de la liste: ${error.message}`);
    return false;
  }
}

// Fonction principale de test
async function runTests() {
  console.log('🧪 Démarrage des tests du système d\'authentification AquaTrack');
  console.log('================================================================');
  console.log('');

  // Test 1: Vérifier que le serveur répond
  try {
    const response = await fetch(`${API_BASE_URL}/`);
    const data = await response.json();
    console.log('✅ Serveur accessible:', data.message);
  } catch (error) {
    console.log('❌ Serveur non accessible. Assurez-vous qu\'il est démarré sur le port 4000');
    return;
  }

  console.log('');

  // Test 2: Connexion avec les comptes de test
  console.log('📋 Test des connexions...');
  const adminToken = await testLogin('<EMAIL>', 'Admin123');
  const techToken = await testLogin('<EMAIL>', 'Tech123');
  
  console.log('');

  // Test 3: Inscription d'un nouvel utilisateur
  console.log('📋 Test d\'inscription...');
  const newUser = {
    nom: 'Test',
    prenom: 'Utilisateur',
    email: '<EMAIL>',
    password: 'Test123',
    adresse: 'Adresse de test',
    tel: '+216 12 345 678',
    role: 'Tech'
  };
  
  const newUserToken = await testRegister(newUser);
  
  console.log('');

  // Test 4: Récupération du profil
  if (adminToken) {
    console.log('📋 Test de récupération du profil (Admin)...');
    await testProfile(adminToken);
  }
  
  if (techToken) {
    console.log('📋 Test de récupération du profil (Tech)...');
    await testProfile(techToken);
  }

  console.log('');

  // Test 5: Liste des utilisateurs (Admin seulement)
  if (adminToken) {
    console.log('📋 Test de la liste des utilisateurs (Admin)...');
    await testUsersList(adminToken);
  }

  if (techToken) {
    console.log('📋 Test de la liste des utilisateurs (Tech - doit échouer)...');
    await testUsersList(techToken);
  }

  console.log('');
  console.log('🎯 Tests terminés !');
  console.log('');
  console.log('📱 Pour tester l\'application mobile:');
  console.log('1. Lancez: start-auth-mobile.bat');
  console.log('2. Utilisez les comptes de test:');
  console.log('   - Admin: <EMAIL> / Admin123');
  console.log('   - Tech: <EMAIL> / Tech123');
}

// Exécution des tests
if (require.main === module) {
  runTests();
}

module.exports = { testLogin, testRegister, testProfile, testUsersList };
