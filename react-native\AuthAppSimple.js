import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  StatusBar,
} from 'react-native';

const API_BASE_URL = 'http://*************:4000'; // Remplacez par votre IP

export default function AuthAppSimple() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Erreur', 'Veuillez saisir votre email et mot de passe');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion:', { email });

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          password: password
        }),
      });

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success) {
        Alert.alert(
          'Connexion réussie',
          `Bienvenue ${data.user.prenom} ${data.user.nom}!\nRôle: ${data.user.role}\nRedirection: ${data.redirectTo}`,
          [
            {
              text: 'OK',
              onPress: () => {
                console.log(`✅ Connexion réussie - Redirection: ${data.redirectTo}`);
              }
            }
          ]
        );
      } else {
        Alert.alert('Erreur de connexion', data.message || 'Erreur de connexion');
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      Alert.alert(
        'Erreur de connexion',
        'Impossible de se connecter au serveur. Vérifiez votre connexion réseau.'
      );
    } finally {
      setLoading(false);
    }
  };

  const fillTestData = (userType) => {
    if (userType === 'admin') {
      setEmail('<EMAIL>');
      setPassword('Admin123');
    } else if (userType === 'tech') {
      setEmail('<EMAIL>');
      setPassword('Tech123');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        
        {/* Logo et titre */}
        <View style={styles.logoContainer}>
          <View style={styles.logoCircle}>
            <Text style={styles.logoIcon}>💧</Text>
          </View>
          <Text style={styles.logoTitle}>AquaTrack</Text>
          <Text style={styles.logoSubtitle}>Authentification Mobile</Text>
        </View>

        {/* Formulaire de connexion */}
        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>Connexion</Text>
          
          {/* Champ Email */}
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              editable={!loading}
            />
          </View>

          {/* Champ Mot de passe */}
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Mot de passe"
              value={password}
              onChangeText={setPassword}
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
              editable={!loading}
            />
          </View>

          {/* Bouton de connexion */}
          <TouchableOpacity
            style={[styles.loginButton, loading && styles.loginButtonDisabled]}
            onPress={handleLogin}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.loginButtonText}>Se connecter</Text>
            )}
          </TouchableOpacity>

          {/* Boutons de test rapide */}
          <View style={styles.testButtonsContainer}>
            <Text style={styles.testTitle}>Tests rapides :</Text>
            <View style={styles.testButtonsRow}>
              <TouchableOpacity
                style={[styles.testButton, { backgroundColor: '#2196F3' }]}
                onPress={() => fillTestData('admin')}
                disabled={loading}
              >
                <Text style={styles.testButtonText}>Admin</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.testButton, { backgroundColor: '#4CAF50' }]}
                onPress={() => fillTestData('tech')}
                disabled={loading}
              >
                <Text style={styles.testButtonText}>Tech</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Informations */}
          <View style={styles.infoContainer}>
            <Text style={styles.infoTitle}>📱 Version Mobile Simplifiée</Text>
            <Text style={styles.infoText}>• Admin: <EMAIL> / Admin123</Text>
            <Text style={styles.infoText}>• Tech: <EMAIL> / Tech123</Text>
            <Text style={styles.infoText}>• Serveur: {API_BASE_URL}</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  logoIcon: {
    fontSize: 40,
  },
  logoTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 5,
  },
  logoSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 30,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    marginBottom: 20,
    backgroundColor: '#f9f9f9',
  },
  input: {
    height: 50,
    paddingHorizontal: 15,
    fontSize: 16,
    color: '#333',
  },
  loginButton: {
    backgroundColor: '#2196F3',
    borderRadius: 10,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 25,
  },
  loginButtonDisabled: {
    backgroundColor: '#ccc',
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  testButtonsContainer: {
    marginBottom: 25,
  },
  testTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  testButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  testButton: {
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    backgroundColor: '#e3f2fd',
    padding: 20,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#1976d2',
    marginBottom: 5,
    fontFamily: 'monospace',
  },
});
