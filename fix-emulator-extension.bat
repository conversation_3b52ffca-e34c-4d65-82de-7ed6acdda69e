@echo off
echo ========================================
echo   CORRECTION EXTENSION ANDROID iOS EMULATOR
========================================
echo.

echo 🔧 Étape 1: Vérification des permissions...
echo.

set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
set "EMULATOR_PATH=%ANDROID_HOME%\emulator"
set "TOOLS_PATH=%ANDROID_HOME%\tools"
set "PLATFORM_TOOLS=%ANDROID_HOME%\platform-tools"

echo 📁 Vérification des dossiers Android SDK...
if exist "%ANDROID_HOME%" (
    echo ✅ Android SDK: %ANDROID_HOME%
) else (
    echo ❌ Android SDK non trouvé
    goto :error
)

if exist "%EMULATOR_PATH%" (
    echo ✅ Emulator: %EMULATOR_PATH%
) else (
    echo ❌ Dossier emulator non trouvé
)

if exist "%PLATFORM_TOOLS%" (
    echo ✅ Platform-tools: %PLATFORM_TOOLS%
) else (
    echo ❌ Platform-tools non trouvé
)

echo.
echo 🔄 Étape 2: Mise à jour des variables d'environnement...

REM Supprimer les anciennes variables
reg delete "HKCU\Environment" /v ANDROID_HOME /f >nul 2>&1
reg delete "HKCU\Environment" /v ANDROID_SDK_ROOT /f >nul 2>&1

REM Ajouter les nouvelles variables
setx ANDROID_HOME "%ANDROID_HOME%"
setx ANDROID_SDK_ROOT "%ANDROID_HOME%"

echo.
echo 📝 Étape 3: Création du fichier de configuration VS Code...

set "VSCODE_SETTINGS=%APPDATA%\Code\User\settings.json"
echo Configuration VS Code: %VSCODE_SETTINGS%

echo.
echo ✅ Configuration terminée !
echo.
echo 📋 INSTRUCTIONS IMPORTANTES :
echo 1. Fermez COMPLÈTEMENT VS Code
echo 2. Redémarrez votre ordinateur (recommandé)
echo 3. Rouvrez VS Code
echo 4. Essayez l'extension Android iOS Emulator
echo.

goto :end

:error
echo ❌ Erreur: Android SDK non trouvé
echo 💡 Installez Android Studio d'abord
echo.

:end
pause
