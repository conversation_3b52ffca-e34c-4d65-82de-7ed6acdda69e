const express = require('express');
const cors = require('cors');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { Pool } = require('pg');

const app = express();
const PORT = 4000;

// Configuration JWT
const JWT_SECRET = 'aquatrack_secret_key_2024';
const JWT_EXPIRES_IN = '24h';

// Middleware
app.use(cors({
  origin: '*', // Permettre toutes les origines pour le développement mobile
  credentials: true
}));
app.use(express.json());

// Configuration de la base de données PostgreSQL "Facturation"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

// Test de connexion à la base de données
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Erreur de connexion à la base de données:', err);
  } else {
    console.log('✅ Connexion à la base de données "Facturation" réussie');
    release();
  }
});

// Middleware d'authentification JWT
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Token d\'accès requis'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Token invalide'
      });
    }
    req.user = user;
    next();
  });
};

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur d\'authentification AquaTrack',
    status: 'Fonctionnel',
    database: 'Facturation',
    port: PORT,
    endpoints: {
      login: 'POST /api/auth/login',
      register: 'POST /api/auth/register',
      profile: 'GET /api/auth/profile',
      users: 'GET /api/auth/users'
    }
  });
});

// 🔐 Route de connexion
app.post('/api/auth/login', async (req, res) => {
  console.log('📱 Requête de connexion reçue:', req.body);
  const { email, password } = req.body;

  try {
    // Validation des champs requis
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }

    // Recherche de l'utilisateur dans la table utilisateur
    const query = `
      SELECT 
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        password,
        role,
        is_protected
      FROM utilisateur 
      WHERE email = $1
    `;

    console.log('🔍 Recherche utilisateur avec email:', email);
    const result = await pool.query(query, [email]);

    if (result.rows.length === 0) {
      console.log('❌ Utilisateur non trouvé');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('👤 Utilisateur trouvé:', user.nom, user.prenom, '- Rôle:', user.role);

    // Vérification du mot de passe
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      console.log('❌ Mot de passe incorrect');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    // Génération du token JWT
    const token = jwt.sign(
      { 
        id: user.idtech,
        email: user.email,
        role: user.role 
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    console.log('✅ Connexion réussie pour:', user.nom, user.prenom);

    // Réponse de succès
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role,
        adresse: user.adresse,
        tel: user.tel
      },
      token: token
    });

  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur',
      error: error.message
    });
  }
});

// 📝 Route d'inscription
app.post('/api/auth/register', async (req, res) => {
  console.log('📝 Requête d\'inscription reçue:', req.body);
  const { nom, prenom, email, password, adresse, tel, role } = req.body;

  try {
    // Validation des champs requis
    if (!nom || !prenom || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Nom, prénom, email et mot de passe requis'
      });
    }

    // Vérifier si l'email existe déjà
    const checkEmailQuery = 'SELECT email FROM utilisateur WHERE email = $1';
    const emailExists = await pool.query(checkEmailQuery, [email]);

    if (emailExists.rows.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Cet email est déjà utilisé'
      });
    }

    // Hachage du mot de passe
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Insertion du nouvel utilisateur
    const insertQuery = `
      INSERT INTO utilisateur (nom, prenom, email, password, adresse, tel, role, is_protected)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING idtech, nom, prenom, email, role, adresse, tel
    `;

    const values = [
      nom,
      prenom,
      email,
      hashedPassword,
      adresse || null,
      tel || null,
      role || 'Tech',
      false
    ];

    const result = await pool.query(insertQuery, values);
    const newUser = result.rows[0];

    console.log('✅ Nouvel utilisateur créé:', newUser.nom, newUser.prenom);

    // Génération du token JWT
    const token = jwt.sign(
      { 
        id: newUser.idtech,
        email: newUser.email,
        role: newUser.role 
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    res.status(201).json({
      success: true,
      message: 'Inscription réussie',
      user: newUser,
      token: token
    });

  } catch (error) {
    console.error('❌ Erreur lors de l\'inscription:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur',
      error: error.message
    });
  }
});

// 👤 Route pour obtenir le profil utilisateur
app.get('/api/auth/profile', authenticateToken, async (req, res) => {
  try {
    const query = `
      SELECT idtech, nom, prenom, email, role, adresse, tel
      FROM utilisateur 
      WHERE idtech = $1
    `;

    const result = await pool.query(query, [req.user.id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    res.json({
      success: true,
      user: result.rows[0]
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur'
    });
  }
});

// 👥 Route pour lister les utilisateurs (Admin seulement)
app.get('/api/auth/users', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Accès refusé - Administrateur requis'
      });
    }

    const query = `
      SELECT idtech, nom, prenom, email, role, adresse, tel, is_protected
      FROM utilisateur 
      ORDER BY nom, prenom
    `;

    const result = await pool.query(query);

    res.json({
      success: true,
      users: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur'
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur d'authentification AquaTrack démarré sur le port ${PORT}`);
  console.log(`📱 API disponible sur: http://localhost:${PORT}`);
  console.log(`🔐 Endpoints disponibles:`);
  console.log(`   POST http://localhost:${PORT}/api/auth/login`);
  console.log(`   POST http://localhost:${PORT}/api/auth/register`);
  console.log(`   GET  http://localhost:${PORT}/api/auth/profile`);
  console.log(`   GET  http://localhost:${PORT}/api/auth/users`);
});
