{"name": "@expo/metro-runtime", "version": "5.0.4", "description": "Tools for making advanced Metro bundler features work", "sideEffects": true, "main": "src/index.ts", "types": "build", "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/metro-runtime", "keywords": [], "author": "650 Industries, Inc.", "license": "MIT", "files": ["build", "src", "rsc", "async-require.js", "async-require.d.ts", "error-overlay.js", "error-overlay.d.ts", "assets", "!**/__tests__"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git"}, "peerDependencies": {"react-native": "*"}, "gitHead": "45bd8a319fb2d6abadac8dcb0c821a55a1f0caa3"}