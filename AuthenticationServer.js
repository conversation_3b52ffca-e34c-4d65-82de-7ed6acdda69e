const express = require('express');
const cors = require('cors');
const bcrypt = require('bcrypt');
const { Pool } = require('pg');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors({
  origin: '*',
  credentials: true
}));
app.use(express.json());

// Configuration PostgreSQL - Base de données "Facturation"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

// Test de connexion
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Erreur de connexion à la base de données:', err);
  } else {
    console.log('✅ Connexion à la base de données "Facturation" réussie');
    release();
  }
});

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur d\'authentification AquaTrack',
    status: 'Fonctionnel',
    database: 'Facturation',
    port: PORT
  });
});

// 🔐 Route de connexion
app.post('/api/auth/login', async (req, res) => {
  console.log('📱 Requête de connexion reçue:', req.body);
  const { email, password } = req.body;

  try {
    // Validation des champs
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }

    // Recherche utilisateur dans la table utilisateur
    const query = `
      SELECT 
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        password,
        role,
        is_protected
      FROM utilisateur 
      WHERE email = $1
    `;

    console.log('🔍 Recherche utilisateur:', email);
    const result = await pool.query(query, [email]);

    if (result.rows.length === 0) {
      console.log('❌ Utilisateur non trouvé');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('👤 Utilisateur trouvé:', user.nom, user.prenom, '- Rôle:', user.role);

    // Vérification du mot de passe (support mot de passe en clair et haché)
    let isPasswordValid = false;
    
    if (user.password.startsWith('$2b$')) {
      // Mot de passe haché avec bcrypt
      isPasswordValid = await bcrypt.compare(password, user.password);
    } else {
      // Mot de passe en clair (pour compatibilité)
      isPasswordValid = (password === user.password);
    }
    
    if (!isPasswordValid) {
      console.log('❌ Mot de passe incorrect');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    // Déterminer la redirection selon le rôle
    let redirectTo = '';
    if (user.role === 'Tech') {
      redirectTo = 'TechnicianDashboard';
    } else if (user.role === 'Admin') {
      redirectTo = 'Dashboard';
    } else {
      redirectTo = 'Dashboard'; // Par défaut
    }

    console.log('✅ Connexion réussie - Redirection vers:', redirectTo);

    // Réponse de succès
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role,
        adresse: user.adresse,
        tel: user.tel
      },
      redirectTo: redirectTo,
      token: `auth_token_${user.idtech}_${Date.now()}`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur',
      error: error.message
    });
  }
});

// 📝 Route d'inscription (optionnelle)
app.post('/api/auth/register', async (req, res) => {
  const { nom, prenom, email, password, adresse, tel, role } = req.body;

  try {
    // Validation
    if (!nom || !prenom || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Nom, prénom, email et mot de passe requis'
      });
    }

    // Vérifier si l'email existe
    const checkQuery = 'SELECT email FROM utilisateur WHERE email = $1';
    const emailExists = await pool.query(checkQuery, [email]);

    if (emailExists.rows.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Cet email est déjà utilisé'
      });
    }

    // Hacher le mot de passe
    const hashedPassword = await bcrypt.hash(password, 10);

    // Insérer le nouvel utilisateur
    const insertQuery = `
      INSERT INTO utilisateur (nom, prenom, email, password, adresse, tel, role, is_protected)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING idtech, nom, prenom, email, role, adresse, tel
    `;

    const values = [
      nom,
      prenom,
      email,
      hashedPassword,
      adresse || null,
      tel || null,
      role || 'Tech',
      false
    ];

    const result = await pool.query(insertQuery, values);
    const newUser = result.rows[0];

    console.log('✅ Nouvel utilisateur créé:', newUser.nom, newUser.prenom);

    // Déterminer la redirection
    const redirectTo = newUser.role === 'Tech' ? 'TechnicianDashboard' : 'Dashboard';

    res.status(201).json({
      success: true,
      message: 'Inscription réussie',
      user: newUser,
      redirectTo: redirectTo,
      token: `auth_token_${newUser.idtech}_${Date.now()}`
    });

  } catch (error) {
    console.error('❌ Erreur lors de l\'inscription:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur'
    });
  }
});

// 🧪 Route pour créer des utilisateurs de test
app.post('/api/create-test-users', async (req, res) => {
  try {
    console.log('🧪 Création des utilisateurs de test...');

    // Supprimer les utilisateurs de test existants
    await pool.query(`
      DELETE FROM utilisateur WHERE email IN (
        '<EMAIL>',
        '<EMAIL>'
      )
    `);

    // Créer les mots de passe hachés
    const adminPassword = await bcrypt.hash('Admin123', 10);
    const techPassword = await bcrypt.hash('Tech123', 10);

    // Insérer les utilisateurs de test
    const testUsers = [
      {
        nom: 'Admin',
        prenom: 'Système',
        email: '<EMAIL>',
        password: adminPassword,
        role: 'Admin',
        adresse: 'Adresse Admin',
        tel: '+216 71 123 456',
        is_protected: true
      },
      {
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        password: techPassword,
        role: 'Tech',
        adresse: 'Adresse Tech',
        tel: '+216 74 987 654',
        is_protected: true
      }
    ];

    for (const user of testUsers) {
      const query = `
        INSERT INTO utilisateur (nom, prenom, email, password, adresse, tel, role, is_protected)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `;
      
      await pool.query(query, [
        user.nom, user.prenom, user.email, user.password,
        user.adresse, user.tel, user.role, user.is_protected
      ]);
      
      console.log(`✅ Utilisateur créé: ${user.email} (${user.role})`);
    }

    res.json({
      success: true,
      message: 'Utilisateurs de test créés avec succès',
      users: [
        { email: '<EMAIL>', password: 'Admin123', role: 'Admin' },
        { email: '<EMAIL>', password: 'Tech123', role: 'Tech' }
      ]
    });

  } catch (error) {
    console.error('❌ Erreur lors de la création des utilisateurs de test:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création des utilisateurs de test'
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur d'authentification démarré sur le port ${PORT}`);
  console.log(`📱 API disponible sur: http://localhost:${PORT}`);
  console.log(`🔐 Route de connexion: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`📝 Route d'inscription: POST http://localhost:${PORT}/api/auth/register`);
  console.log(`🧪 Créer utilisateurs test: POST http://localhost:${PORT}/api/create-test-users`);
  console.log('');
  console.log('🎯 Comptes de test disponibles:');
  console.log('   Admin: <EMAIL> / Admin123 → Dashboard');
  console.log('   Tech: <EMAIL> / Tech123 → TechnicianDashboard');
});
