-- Script d'insertion des utilisateurs de test pour AquaTrack
-- Base de données: Facturation

-- Suppression des utilisateurs de test existants (optionnel)
DELETE FROM utilisateur WHERE email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
);

-- Insertion des utilisateurs de test avec mots de passe hachés
-- Note: Les mots de passe sont hachés avec bcrypt (rounds=10)

-- 1. Administrateur principal
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) VALUES
(
    '<PERSON><PERSON><PERSON>',
    'Fatima',
    '123 Avenue Habib Bourguiba, Tunis',
    '+216 71 123 456',
    '<EMAIL>',
    '$2b$10$rQZ8kHWKQVXGxqKrKrKrKe7QZ8kHWKQVXGxqKrKrKrKe7QZ8kHWKQV', -- Password: Admin123
    'Admin',
    true
);

-- 2. Technicien principal
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) VALUES
(
    'Benali',
    'Ahmed',
    '456 Rue de la République, Sfax',
    '+216 74 987 654',
    '<EMAIL>',
    '$2b$10$rQZ8kHWKQVXGxqKrKrKrKe7QZ8kHWKQVXGxqKrKrKrKe7QZ8kHWKQV', -- Password: Tech123
    'Tech',
    true
);

-- 3. Technicien Ahmed Benali
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) VALUES
(
    'Benali',
    'Ahmed',
    '789 Boulevard du 14 Janvier, Sousse',
    '+216 73 555 123',
    '<EMAIL>',
    '$2b$10$rQZ8kHWKQVXGxqKrKrKrKe7QZ8kHWKQVXGxqKrKrKrKe7QZ8kHWKQV', -- Password: Ahmed123
    'Tech',
    false
);

-- 4. Administrateur Fatima Gharbi
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) VALUES
(
    'Gharbi',
    'Fatima',
    '321 Avenue de la Liberté, Monastir',
    '+216 73 444 789',
    '<EMAIL>',
    '$2b$10$rQZ8kHWKQVXGxqKrKrKrKe7QZ8kHWKQVXGxqKrKrKrKe7QZ8kHWKQV', -- Password: Fatima123
    'Admin',
    false
);

-- 5. Technicien Mohamed Salem
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) VALUES
(
    'Salem',
    'Mohamed',
    '654 Rue des Martyrs, Gabès',
    '+216 75 333 456',
    '<EMAIL>',
    '$2b$10$rQZ8kHWKQVXGxqKrKrKrKe7QZ8kHWKQVXGxqKrKrKrKe7QZ8kHWKQV', -- Password: Mohamed123
    'Tech',
    false
);

-- Vérification des insertions
SELECT 
    idtech,
    nom,
    prenom,
    email,
    role,
    is_protected,
    created_at
FROM utilisateur 
ORDER BY role DESC, nom, prenom;

-- Affichage du nombre d'utilisateurs créés
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN role = 'Admin' THEN 1 END) as admin_count,
    COUNT(CASE WHEN role = 'Tech' THEN 1 END) as tech_count
FROM utilisateur;

-- Message de confirmation
SELECT 'Utilisateurs de test créés avec succès!' as message;
