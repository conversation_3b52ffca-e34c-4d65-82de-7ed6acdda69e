@echo off
echo ========================================
echo   Démarrage de l'Application Mobile
echo ========================================
echo.

cd auth-mobile

echo 1. Vérification des dépendances...
if not exist node_modules (
    echo Installation des dépendances...
    npm install
    echo.
)

echo 2. Démarrage de l'application React Native...
echo.
echo Application mobile d'authentification AquaTrack
echo.
echo Instructions:
echo 1. Assurez-vous que le backend est démarré (port 4000)
echo 2. Installez Expo Go sur votre téléphone
echo 3. Scannez le QR code qui apparaîtra
echo 4. Ou appuyez sur 'w' pour ouvrir dans le navigateur
echo.
echo Comptes de test:
echo   Admin: <EMAIL> / Admin123
echo   Tech: <EMAIL> / Tech123
echo.

npm start

pause
