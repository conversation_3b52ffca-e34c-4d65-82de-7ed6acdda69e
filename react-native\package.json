{"name": "aquatrack-mobile", "version": "1.0.0", "main": "AppSimple.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.0", "expo-status-bar": "~2.0.0", "react": "18.2.0", "react-native": "0.76.5", "react-native-screens": "~4.1.0", "react-native-safe-area-context": "~4.12.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "react-native-gesture-handler": "~2.20.0", "react-native-reanimated": "~3.16.0", "@expo/vector-icons": "^14.0.0", "@react-native-picker/picker": "2.8.1", "expo-camera": "~16.1.10", "expo-barcode-scanner": "~14.0.0", "expo-location": "~18.0.0", "react-native-maps": "1.18.0", "expo-file-system": "~18.0.0", "expo-sharing": "~13.0.0", "@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "1.21.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}