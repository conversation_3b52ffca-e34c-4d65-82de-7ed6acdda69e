{"version": 3, "names": ["_AsyncStorage", "_interopRequireDefault", "require", "obj", "__esModule", "default", "useAsyncStorage", "key", "getItem", "args", "AsyncStorage", "setItem", "mergeItem", "removeItem"], "sources": ["hooks.ts"], "sourcesContent": ["import AsyncStorage from \"./AsyncStorage\";\nimport type { AsyncStorageHook } from \"./types\";\n\nexport function useAsyncStorage(key: string): AsyncStorageHook {\n  return {\n    getItem: (...args) => AsyncStorage.getItem(key, ...args),\n    setItem: (...args) => AsyncStorage.setItem(key, ...args),\n    mergeItem: (...args) => AsyncStorage.mergeItem(key, ...args),\n    removeItem: (...args) => AsyncStorage.removeItem(key, ...args),\n  };\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0C,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAGnC,SAASG,eAAeA,CAACC,GAAW,EAAoB;EAC7D,OAAO;IACLC,OAAO,EAAEA,CAAC,GAAGC,IAAI,KAAKC,qBAAY,CAACF,OAAO,CAACD,GAAG,EAAE,GAAGE,IAAI,CAAC;IACxDE,OAAO,EAAEA,CAAC,GAAGF,IAAI,KAAKC,qBAAY,CAACC,OAAO,CAACJ,GAAG,EAAE,GAAGE,IAAI,CAAC;IACxDG,SAAS,EAAEA,CAAC,GAAGH,IAAI,KAAKC,qBAAY,CAACE,SAAS,CAACL,GAAG,EAAE,GAAGE,IAAI,CAAC;IAC5DI,UAAU,EAAEA,CAAC,GAAGJ,IAAI,KAAKC,qBAAY,CAACG,UAAU,CAACN,GAAG,EAAE,GAAGE,IAAI;EAC/D,CAAC;AACH"}