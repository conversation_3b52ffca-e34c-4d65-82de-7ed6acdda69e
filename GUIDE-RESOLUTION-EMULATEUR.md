# Guide de Résolution - Erreur Émulateur Android

## Problème
L'application "Android iOS Emulator" affiche l'erreur : "Error fetching your Android emulators! Make sure your path is correct..."

## Solutions

### Solution 1: Utiliser Expo Go (RECOMMANDÉ)
Votre projet utilise Expo, la méthode la plus simple est d'utiliser Expo Go :

1. **Installer Expo Go sur votre téléphone**
   - Android: https://play.google.com/store/apps/details?id=host.exp.exponent
   - iOS: https://apps.apple.com/app/expo-go/id982107779

2. **Démarrer le projet**
   ```bash
   cd react-native
   npm start
   ```

3. **Scanner le QR code** avec Expo Go pour tester sur votre téléphone

### Solution 2: Utiliser Android Studio (Pour émulateur PC)

1. **Installer Android Studio**
   - Télécharger depuis: https://developer.android.com/studio

2. **Configurer l'émulateur**
   - Ouvrir Android Studio
   - Aller dans Tools > AVD Manager
   - Créer un nouveau Virtual Device
   - Choisir un appareil (ex: Pixel 4)
   - Télécharger une image système (API 30+)
   - Démarrer l'émulateur

3. **Configurer les variables d'environnement**
   ```bash
   # Ajouter au PATH système:
   C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
   C:\Users\<USER>\AppData\Local\Android\Sdk\tools
   ```

4. **Lancer le projet**
   ```bash
   cd react-native
   npm run android
   ```

### Solution 3: Utiliser Metro Bundler avec navigateur

1. **Démarrer le serveur de développement**
   ```bash
   cd react-native
   npm start
   ```

2. **Ouvrir dans le navigateur**
   - Appuyer sur 'w' dans le terminal pour ouvrir dans le navigateur web

## Commandes utiles

### Vérifier la configuration
```bash
cd react-native
npx expo doctor
```

### Nettoyer le cache
```bash
cd react-native
npx expo start --clear
```

### Installer les dépendances manquantes
```bash
cd react-native
npm install
```

## Fichiers de lancement automatique

Créer des fichiers batch pour faciliter le lancement :

### start-expo.bat
```batch
@echo off
cd react-native
npm start
pause
```

### start-android.bat
```batch
@echo off
cd react-native
npm run android
pause
```

## Dépannage

### Si npm start ne fonctionne pas :
```bash
cd react-native
npm install -g @expo/cli
expo start
```

### Si l'émulateur Android ne se connecte pas :
```bash
adb devices
adb reverse tcp:8081 tcp:8081
```

### Si le port est occupé :
```bash
npx expo start --port 8082
```

## Recommandation

Pour votre projet de facturation mobile, je recommande d'utiliser **Expo Go** sur votre téléphone pour les tests rapides, et **Android Studio** pour les tests d'émulateur sur PC.

L'application "Android iOS Emulator" que vous utilisez semble être une application tierce qui peut avoir des problèmes de compatibilité avec les projets Expo.
