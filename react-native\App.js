import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

// Import des écrans d'authentification
import AuthenticationScreen from './screens/AuthenticationScreen';
import TechnicianDashboard from './screens/TechnicianDashboard';
import Dashboard from './screens/Dashboard';

// Import des écrans existants
import ClientsListScreen from './screens/ClientsListScreen';
import ConsommationScreen from './screens/ConsommationScreen';
import FacturesScreen from './screens/FacturesScreen';
import ScannerScreen from './screens/ScannerScreen';
import MapScreen from './screens/MapScreen';

const Stack = createStackNavigator();

// Écran d'accueil simple
const HomeScreen = () => {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      <View style={styles.homeContainer}>
        <View style={styles.logoContainer}>
          <View style={styles.logoCircle}>
            <Ionicons name="water" size={60} color="#2196F3" />
          </View>
          <Text style={styles.logoTitle}>AquaTrack</Text>
          <Text style={styles.logoSubtitle}>Système de Facturation Mobile</Text>
        </View>

        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeTitle}>Bienvenue !</Text>
          <Text style={styles.welcomeText}>
            Utilisez le menu latéral pour accéder aux différentes fonctionnalités de l'application.
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

// Navigation avec Drawer pour les pages principales
const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: '#2196F3',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        drawerStyle: {
          backgroundColor: '#f8fafc',
          width: 280,
        },
      }}
    >
      <Drawer.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: 'Accueil',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="ClientsList"
        component={ClientsListScreen}
        options={{
          title: 'Liste des Clients',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="people-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Consommation"
        component={ConsommationScreen}
        options={{
          title: 'Saisie Consommation',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="water-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Factures"
        component={FacturesScreen}
        options={{
          title: 'Factures',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="document-text-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Scanner"
        component={ScannerScreen}
        options={{
          title: 'Scanner QR Code',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="qr-code-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Map"
        component={MapScreen}
        options={{
          title: 'Localisation',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="map-outline" size={size} color={color} />
          ),
        }}
      />
    </Drawer.Navigator>
  );
};

// Application principale avec authentification
export default function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="AuthenticationScreen"
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen
          name="AuthenticationScreen"
          component={AuthenticationScreen}
          options={{ title: 'Connexion' }}
        />
        <Stack.Screen
          name="TechnicianDashboard"
          component={TechnicianDashboard}
          options={{ title: 'Dashboard Technicien' }}
        />
        <Stack.Screen
          name="Dashboard"
          component={Dashboard}
          options={{ title: 'Dashboard Admin' }}
        />
        <Stack.Screen
          name="ClientsList"
          component={ClientsListScreen}
          options={{ title: 'Liste des Clients' }}
        />
        <Stack.Screen
          name="Consommation"
          component={ConsommationScreen}
          options={{ title: 'Saisie Consommation' }}
        />
        <Stack.Screen
          name="Factures"
          component={FacturesScreen}
          options={{ title: 'Factures' }}
        />
        <Stack.Screen
          name="Scanner"
          component={ScannerScreen}
          options={{ title: 'Scanner QR Code' }}
        />
        <Stack.Screen
          name="Map"
          component={MapScreen}
          options={{ title: 'Localisation' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  homeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  logoCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  logoTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 8,
  },
  logoSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  welcomeContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    alignItems: 'center',
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 15,
  },
  welcomeText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
});


