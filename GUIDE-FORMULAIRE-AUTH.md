# 🔐 Guide - Formulaire d'Authentification

## ✅ Formulaire d'Authentification Créé !

J'ai créé un **formulaire d'authentification complet** qui utilise votre table `utilisateur` de la base de données "Facturation".

## 📱 Ce qui a été créé :

### **Fichier Principal :**
- **`react-native/AuthApp.js`** - Formulaire d'authentification complet

### **Fonctionnalités du Formulaire :**
- ✅ **Champs Email et Mot de passe**
- ✅ **Boutons de test rapide** (Admin/Tech)
- ✅ **Connexion à votre base "Facturation"**
- ✅ **Vérification dans la table `utilisateur`**
- ✅ **Redirection selon le rôle** (Admin/Tech)
- ✅ **Interface mobile responsive**

## 🚀 Comment Utiliser :

### **1. Créer les Utilisateurs de Test**
Exécutez ce SQL dans votre base "Facturation" :

```sql
-- Supprimer les utilisateurs existants (optionnel)
DELETE FROM utilisateur WHERE email IN (
    '<EMAIL>',
    '<EMAIL>'
);

-- <PERSON><PERSON>er l'administrateur
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) VALUES
('Admin', 'Système', '123 Avenue Principale, Tunis', '+216 71 123 456', '<EMAIL>', 'Admin123', 'Admin', true);

-- Créer le technicien
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) VALUES
('Technicien', 'Test', '456 Rue Secondaire, Sfax', '+216 74 987 654', '<EMAIL>', 'Tech123', 'Tech', true);
```

### **2. Démarrer le Système**

#### **Option 1 : Script Automatique**
```bash
# Double-cliquez sur :
start-auth-complete.bat
```

#### **Option 2 : Démarrage Manuel**
```bash
# Terminal 1 - Serveur d'authentification
node AuthenticationServer.js

# Terminal 2 - Application React Native
cd react-native
npx expo start --web --port 8082 --offline
```

### **3. Tester l'Authentification**

1. **Ouvrez** : http://localhost:8082
2. **Utilisez les boutons de test** :
   - Cliquez sur **"Admin"** pour remplir automatiquement
   - Cliquez sur **"Tech"** pour remplir automatiquement
3. **Cliquez** sur "Se connecter"
4. **Vérifiez** la redirection selon le rôle

## 🔑 Comptes de Test

| Email | Mot de passe | Rôle | Redirection |
|-------|--------------|------|-------------|
| `<EMAIL>` | `Admin123` | Admin | → Dashboard |
| `<EMAIL>` | `Tech123` | Tech | → TechnicianDashboard |

## 🗄️ Configuration Base de Données

### **Table Utilisée :**
```sql
-- Table: utilisateur (Base: Facturation)
CREATE TABLE utilisateur (
    idtech SERIAL PRIMARY KEY,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    adresse VARCHAR(255),
    tel VARCHAR(20),
    email VARCHAR(100),      -- Utilisé pour la connexion
    password VARCHAR(100),   -- Mot de passe (clair ou haché)
    role VARCHAR(10),        -- 'Admin' ou 'Tech'
    is_protected BOOLEAN
);
```

### **Configuration Serveur :**
```javascript
// Dans AuthenticationServer.js
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',  // Votre base
  password: '123456',
  port: 5432,
});
```

## 🎯 Fonctionnement du Formulaire

### **1. Interface Utilisateur**
- **Logo AquaTrack** avec icône eau 💧
- **Champs de saisie** avec icônes 📧 🔒
- **Bouton "Afficher/Masquer"** le mot de passe 👁️
- **Boutons de test rapide** Admin/Tech
- **Informations de debug** (base de données, comptes)

### **2. Logique d'Authentification**
```javascript
// Envoi vers le serveur
POST http://localhost:4000/api/auth/login
{
  "email": "<EMAIL>",
  "password": "Admin123"
}

// Réponse du serveur
{
  "success": true,
  "user": { "nom": "Admin", "prenom": "Système", "role": "Admin" },
  "redirectTo": "Dashboard"
}
```

### **3. Redirection Automatique**
- **Role = 'Admin'** → Redirection vers `Dashboard`
- **Role = 'Tech'** → Redirection vers `TechnicianDashboard`

## 🔧 Personnalisation

### **Ajouter des Utilisateurs :**
```sql
INSERT INTO utilisateur (nom, prenom, email, password, role, is_protected)
VALUES ('Nouveau', 'Utilisateur', '<EMAIL>', 'motdepasse', 'Tech', false);
```

### **Modifier l'Interface :**
Éditez `react-native/AuthApp.js` pour :
- Changer les couleurs
- Modifier le logo
- Ajouter des champs
- Personnaliser les messages

## 🐛 Dépannage

### **Si le formulaire ne s'affiche pas :**
1. Vérifiez que `package.json` utilise `"main": "AuthApp.js"`
2. Redémarrez l'application : `npx expo start --clear`

### **Si la connexion échoue :**
1. Vérifiez que le serveur d'auth fonctionne : http://localhost:4000
2. Vérifiez que les utilisateurs existent dans la base
3. Consultez les logs du serveur

### **Si la base de données ne répond pas :**
1. Vérifiez PostgreSQL
2. Vérifiez la base "Facturation"
3. Vérifiez les paramètres de connexion

## 📱 URLs Importantes

- **Application** : http://localhost:8082
- **Serveur d'auth** : http://localhost:4000
- **Test serveur** : http://localhost:4000 (doit afficher un JSON)

---

**✅ Votre formulaire d'authentification est maintenant fonctionnel !**

Il utilise votre table `utilisateur` de la base "Facturation" avec redirection automatique selon le rôle.
